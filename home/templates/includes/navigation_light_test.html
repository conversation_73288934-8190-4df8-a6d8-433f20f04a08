{% load static %}

<!-- Navbar Light -->
<nav class="navbar navbar-expand-lg navbar-light blur bg-white py-3 position-sticky z-index-sticky top-0 shadow">
  <div class="container">

    <a class="navbar-brand" href="/" rel="tooltip" title="CASHBACK GOAT Saves You More!" data-placement="bottom">
      CASHBACK GOAT
    </a>

    <button class="navbar-toggler shadow-none ms-2" type="button" data-bs-toggle="collapse" data-bs-target="#navigation"
            aria-controls="navigation" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon mt-2">
        <span class="navbar-toggler-bar bar1"></span>
        <span class="navbar-toggler-bar bar2"></span>
        <span class="navbar-toggler-bar bar3"></span>
      </span>
    </button>

    <!-- Start collapsible search bar for small screen -->
    <div class="container d-block d-lg-none mx-0 px-1">
      <div class="row justify-space-between  py-0 mt-auto mx-1">
        <div class="col-auto mx-auto px-0">
          <div class="input-group mx-0" style="position:relative">
          <form action="{% url 'search' %}" method="GET" id="search_store">
              <input class="form-control px-0" placeholder="Search Stores and coupons" name="search_store" type="search" size="100">
                <button type="submit" form="search_store" class="btn btn-primary w-auto me-0 mb-0"
                        style="position: absolute;top: 0rem;right: 0rem;">
                <i class="fas fa-search" aria-hidden="true"></i>
              </button>
          </form>
          </div>
        </div>
      </div>
    </div>


    <div class="collapse navbar-collapse w-100 pt-3 pb-2 py-lg-0 ms-lg-2 ps-lg-3" id="navigation">
      <ul class="navbar-nav navbar-nav-hover w-100">

        <!--Start Categories dropdown -->
        <li class="nav-item dropdown dropdown-hover mx-2 ms-lg-5">
          <a class="nav-link ps-2 d-flex justify-content-between cursor-pointer align-items-center"
             id="dropdownMenuPages5" data-bs-toggle="dropdown" aria-expanded="false">
            Categories
            <img src="{% static 'assets/img/down-arrow-dark.svg' %}" alt="down-arrow" class="arrow ms-1">
          </a>
          <div class="dropdown-menu dropdown-menu-animation dropdown-md p-3 border-radius-lg mt-0 mt-lg-3"
               aria-labelledby="dropdownMenuPages5">
            <div class="d-none d-lg-block">
              <div
                  class="dropdown-header text-dark font-weight-bolder d-flex justify-content-cente align-items-center px-0">
                <div class="d-inline-block">
                  <div class="icon icon-shape icon-xs border-radius-md bg-gradient-primary text-center me-2 d-flex align-items-center justify-content-center">
                    <svg width="12px" height="12px" viewBox="0 0 45 40" version="1.1" xmlns="http://www.w3.org/2000/svg"
                         xmlns:xlink="http://www.w3.org/1999/xlink">
                      <title>shop </title>
                      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <g transform="translate(-1716.000000, -439.000000)" fill="#FFFFFF" fill-rule="nonzero">
                          <g transform="translate(1716.000000, 291.000000)">
                            <g transform="translate(0.000000, 148.000000)">
                              <path
                                  d="M46.7199583,10.7414583 L40.8449583,0.949791667 C40.4909749,0.360605034 39.8540131,0 39.1666667,0 L7.83333333,0 C7.1459869,0 6.50902508,0.360605034 6.15504167,0.949791667 L0.280041667,10.7414583 C0.0969176761,11.0460037 -1.23209662e-05,11.3946378 -1.23209662e-05,11.75 C-0.00758042603,16.0663731 3.48367543,19.5725301 7.80004167,19.5833333 L7.81570833,19.5833333 C9.75003686,19.5882688 11.6168794,18.8726691 13.0522917,17.5760417 C16.0171492,20.2556967 20.5292675,20.2556967 23.494125,17.5760417 C26.4604562,20.2616016 30.9794188,20.2616016 33.94575,17.5760417 C36.2421905,19.6477597 39.5441143,20.1708521 42.3684437,18.9103691 C45.1927731,17.649886 47.0084685,14.8428276 47.0000295,11.75 C47.0000295,11.3946378 46.9030823,11.0460037 46.7199583,10.7414583 Z"
                                  opacity="0.598981585"></path>
                              <path
                                  d="M39.198,22.4912623 C37.3776246,22.4928106 35.5817531,22.0149171 33.951625,21.0951667 L33.92225,21.1107282 C31.1430221,22.6838032 27.9255001,22.9318916 24.9844167,21.7998837 C24.4750389,21.605469 23.9777983,21.3722567 23.4960833,21.1018359 L23.4745417,21.1129513 C20.6961809,22.6871153 17.4786145,22.9344611 14.5386667,21.7998837 C14.029926,21.6054643 13.533337,21.3722507 13.0522917,21.1018359 C11.4250962,22.0190609 9.63246555,22.4947009 7.81570833,22.4912623 C7.16510551,22.4842162 6.51607673,22.4173045 5.875,22.2911849 L5.875,44.7220845 C5.875,45.9498589 6.7517757,46.9451667 7.83333333,46.9451667 L19.5833333,46.9451667 L19.5833333,33.6066734 L27.4166667,33.6066734 L27.4166667,46.9451667 L39.1666667,46.9451667 C40.2482243,46.9451667 41.125,45.9498589 41.125,44.7220845 L41.125,22.2822926 C40.4887822,22.4116582 39.8442868,22.4815492 39.198,22.4912623 Z"></path>
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                  </div>
                </div>
                <a href="{% url 'posts-all'%}" class="nav-link ps-2 d-flex justify-content-between cursor-pointer align-items-center">Deals and Coupons →</a>
              </div>
              {#              <a href="/" class="dropdown-item border-radius-md">#}
              {#                <span class="ps-3">Double Cashback Stores</span>#}
              {#              </a>#}
              {#              <a href="{% url 'about-us' %}" class="dropdown-item border-radius-md">#}
              {#                <span class="ps-3">Deals and Coupons</span>#}
              {#              </a>#}
              <div
                  class="dropdown-header text-dark font-weight-bolder d-flex justify-content-cente align-items-center px-0 mt-3">
                <div class="d-inline-block">
                  <div class="icon icon-shape icon-xs border-radius-md bg-gradient-primary text-center me-2 d-flex align-items-center justify-content-center">
                    <svg width="12px" height="12px" viewBox="0 0 45 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                      <title>office</title>
                      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <g transform="translate(-1869.000000, -293.000000)" fill="#FFFFFF" fill-rule="nonzero">
                          <g transform="translate(1716.000000, 291.000000)">
                            <g transform="translate(153.000000, 2.000000)">
                              <path
                                  d="M12.25,17.5 L8.75,17.5 L8.75,1.75 C8.75,0.78225 9.53225,0 10.5,0 L31.5,0 C32.46775,0 33.25,0.78225 33.25,1.75 L33.25,12.25 L29.75,12.25 L29.75,3.5 L12.25,3.5 L12.25,17.5 Z"
                                  opacity="0.6"></path>
                              <path
                                  d="M40.25,14 L24.5,14 C23.53225,14 22.75,14.78225 22.75,15.75 L22.75,38.5 L19.25,38.5 L19.25,22.75 C19.25,21.78225 18.46775,21 17.5,21 L1.75,21 C0.78225,21 0,21.78225 0,22.75 L0,40.25 C0,41.21775 0.78225,42 1.75,42 L40.25,42 C41.21775,42 42,41.21775 42,40.25 L42,15.75 C42,14.78225 41.21775,14 40.25,14 Z M12.25,36.75 L7,36.75 L7,33.25 L12.25,33.25 L12.25,36.75 Z M12.25,29.75 L7,29.75 L7,26.25 L12.25,26.25 L12.25,29.75 Z M35,36.75 L29.75,36.75 L29.75,33.25 L35,33.25 L35,36.75 Z M35,29.75 L29.75,29.75 L29.75,26.25 L35,26.25 L35,29.75 Z M35,22.75 L29.75,22.75 L29.75,19.25 L35,19.25 L35,22.75 Z"></path>
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                  </div>
                </div>
                <a href="{% url 'store-category'%}" class="nav-link ps-2 d-flex justify-content-between cursor-pointer align-items-center">See All Stores →</a>
{#                <a class="nav-link ps-2 d-flex justify-content-between cursor-pointer align-items-center" id="dropdownMenuBlocks" data-bs-toggle="dropdown" aria-expanded="false">#}
{#                  Blocks#}
{#                  <img src="{% static 'assets/img/down-arrow-dark.svg' %}" alt="down-arrow" class="arrow ms-1">#}
{#                </a>#}
              </div>

              {% for store_category in all_store_categories %}
                <a href="{% url 'store-category' category_id=store_category.id %}" class="dropdown-item border-radius-md">
                  <span class="ps-3"> {{ store_category.name }} </span>
                </a>
              {% endfor %}

            </div>
            <!--Start Categories bar for small screens-->
            <div class="d-lg-none">
              <div
                  class="dropdown-header text-dark font-weight-bolder d-flex justify-content-center align-items-center px-0">
                <div class="d-inline-block">
                  <div
                      class="icon icon-shape icon-xs border-radius-md bg-gradient-primary text-center me-2 d-flex align-items-center justify-content-center">
                    <svg width="12px" height="12px" viewBox="0 0 45 40" version="1.1" xmlns="http://www.w3.org/2000/svg"
                         xmlns:xlink="http://www.w3.org/1999/xlink">
                      <title>shop </title>
                      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <g transform="translate(-1716.000000, -439.000000)" fill="#FFFFFF" fill-rule="nonzero">
                          <g transform="translate(1716.000000, 291.000000)">
                            <g transform="translate(0.000000, 148.000000)">
                              <path
                                  d="M46.7199583,10.7414583 L40.8449583,0.949791667 C40.4909749,0.360605034 39.8540131,0 39.1666667,0 L7.83333333,0 C7.1459869,0 6.50902508,0.360605034 6.15504167,0.949791667 L0.280041667,10.7414583 C0.0969176761,11.0460037 -1.23209662e-05,11.3946378 -1.23209662e-05,11.75 C-0.00758042603,16.0663731 3.48367543,19.5725301 7.80004167,19.5833333 L7.81570833,19.5833333 C9.75003686,19.5882688 11.6168794,18.8726691 13.0522917,17.5760417 C16.0171492,20.2556967 20.5292675,20.2556967 23.494125,17.5760417 C26.4604562,20.2616016 30.9794188,20.2616016 33.94575,17.5760417 C36.2421905,19.6477597 39.5441143,20.1708521 42.3684437,18.9103691 C45.1927731,17.649886 47.0084685,14.8428276 47.0000295,11.75 C47.0000295,11.3946378 46.9030823,11.0460037 46.7199583,10.7414583 Z"
                                  opacity="0.598981585"></path>
                              <path
                                  d="M39.198,22.4912623 C37.3776246,22.4928106 35.5817531,22.0149171 33.951625,21.0951667 L33.92225,21.1107282 C31.1430221,22.6838032 27.9255001,22.9318916 24.9844167,21.7998837 C24.4750389,21.605469 23.9777983,21.3722567 23.4960833,21.1018359 L23.4745417,21.1129513 C20.6961809,22.6871153 17.4786145,22.9344611 14.5386667,21.7998837 C14.029926,21.6054643 13.533337,21.3722507 13.0522917,21.1018359 C11.4250962,22.0190609 9.63246555,22.4947009 7.81570833,22.4912623 C7.16510551,22.4842162 6.51607673,22.4173045 5.875,22.2911849 L5.875,44.7220845 C5.875,45.9498589 6.7517757,46.9451667 7.83333333,46.9451667 L19.5833333,46.9451667 L19.5833333,33.6066734 L27.4166667,33.6066734 L27.4166667,46.9451667 L39.1666667,46.9451667 C40.2482243,46.9451667 41.125,45.9498589 41.125,44.7220845 L41.125,22.2822926 C40.4887822,22.4116582 39.8442868,22.4815492 39.198,22.4912623 Z"></path>
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                  </div>
                </div>
                <a href="{% url 'posts-all'%}" class="nav-link ps-2 d-flex justify-content-between cursor-pointer align-items-center">
                  Deals and Coupons →
                </a>
              </div>
              {#                  <a href="/" class="dropdown-item border-radius-md">#}
              {#                      <span class="ps-3">Placeholder</span>#}
              {#                  </a>#}
              <div
                  class="dropdown-header text-dark font-weight-bolder d-flex justify-content-center align-items-center px-0 mt-3">
                <div class="d-inline-block">
                  <div
                      class="icon icon-shape icon-xs border-radius-md bg-gradient-primary text-center me-2 d-flex align-items-center justify-content-center ps-0">
                    <svg width="12px" height="12px" viewBox="0 0 42 42" version="1.1" xmlns="http://www.w3.org/2000/svg"
                         xmlns:xlink="http://www.w3.org/1999/xlink">
                      <title>office</title>
                      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <g transform="translate(-1869.000000, -293.000000)" fill="#FFFFFF" fill-rule="nonzero">
                          <g transform="translate(1716.000000, 291.000000)">
                            <g transform="translate(153.000000, 2.000000)">
                              <path
                                  d="M12.25,17.5 L8.75,17.5 L8.75,1.75 C8.75,0.78225 9.53225,0 10.5,0 L31.5,0 C32.46775,0 33.25,0.78225 33.25,1.75 L33.25,12.25 L29.75,12.25 L29.75,3.5 L12.25,3.5 L12.25,17.5 Z"
                                  opacity="0.6"></path>
                              <path
                                  d="M40.25,14 L24.5,14 C23.53225,14 22.75,14.78225 22.75,15.75 L22.75,38.5 L19.25,38.5 L19.25,22.75 C19.25,21.78225 18.46775,21 17.5,21 L1.75,21 C0.78225,21 0,21.78225 0,22.75 L0,40.25 C0,41.21775 0.78225,42 1.75,42 L40.25,42 C41.21775,42 42,41.21775 42,40.25 L42,15.75 C42,14.78225 41.21775,14 40.25,14 Z M12.25,36.75 L7,36.75 L7,33.25 L12.25,33.25 L12.25,36.75 Z M12.25,29.75 L7,29.75 L7,26.25 L12.25,26.25 L12.25,29.75 Z M35,36.75 L29.75,36.75 L29.75,33.25 L35,33.25 L35,36.75 Z M35,29.75 L29.75,29.75 L29.75,26.25 L35,26.25 L35,29.75 Z M35,22.75 L29.75,22.75 L29.75,19.25 L35,19.25 L35,22.75 Z"></path>
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                  </div>
                </div>
                <a href="{% url 'store-category'%}" class="nav-link ps-2 d-flex justify-content-between cursor-pointer align-items-center">
                  See All Stores
                </a>
              </div>

            </div>

          </div>
        </li>

        <!--Start Search bar for med-xl screens-->
        <li class="nav-item dropdown dropdown-hover mx-2 ms-lg-5 d-none d-lg-block">

          <div class="row text-center py-0 mt-auto mx-1" id="search-box">
            <div class="col-auto mx-auto">
              <div class="input-group mx-xl-0" style="position:relative">
                <form action="{% url 'search' %}" method="GET" id="search_store">
                  <input class="form-control" placeholder="Search Stores and Coupons" type="search" name="search_store"
                         size="65">
                </form>
{#                <button type="submit" form="search_store" class="btn btn-primary w-auto me-0 mb-0"#}
{#                        style="position:absolute;top:0px;right:0px;"><i class="fas fa-search" aria-hidden="true"></i>#}
{#                </button>#}

                <button type="submit" form="search_store" class="btn btn-primary w-auto me-0 mb-0"
                        style="position: absolute;top: 0rem;right: 0rem;"><i class="fas fa-search" aria-hidden="true"></i>
                </button>

              </div>

            </div>
          </div>
        </li>

        <!--Start log in/out and My account -->
        {% if request.user.is_authenticated %}
          {#        <li class="nav-item ms-lg-auto">#}
          {#          <a class="nav-link nav-link-icon me-2" href="{% url 'account_logout' %}">#}
          {#            <i class="fa fa-sign-out me-1"></i>#}
          {#            <p class="d-inline text-sm z-index-1 font-bold" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Logout">Logout</p>#}
          {#          </a>#}
          {#        </li>#}

          <li class="nav-item ms-lg-auto">
            <div class="dropdown">
              <button class="btn btn-outline-primary btn-round mb-0 me-0 mt-2 mt-md-0 dropdown-toggle" type="button"
                      id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                My Account
              </button>
              <ul class="dropdown-menu px-2 py-3" aria-labelledby="dropdownMenuButton">
                <li><a class="dropdown-item border-radius-md" href="{% url 'account_logout' %}">Account Summary</a></li>
                <li><a class="dropdown-item border-radius-md" href="{% url 'account_logout' %}">My Favorites</a></li>
                <li><a class="dropdown-item border-radius-md" href="{% url 'account_logout' %}">My Referrals</a></li>
                <li><a class="dropdown-item border-radius-md" href="{% url 'account_logout' %}">Profile Settings</a></li>
                <li><a class="dropdown-item border-radius-md" href="{% url 'account_logout' %}">Payment Request</a></li>
                <li><a class="dropdown-item border-radius-md" href="{% url 'account_logout' %}">Sign Out</a></li>
              </ul>
            </div>
          </li>
        {% else %}

          <!-- Start Sign In + Sign Up buttons -->
          <li class="nav-item my-auto ms-lg-auto">
            <div class="d-flex justify-content-cente align-items-center px-0">
              <a href="{% url 'account_login' %}" target="_blank"
                 class="btn btn-sm btn-outline-primary  btn-round mb-0 me-1 ms-1 mt-2 mt-md-0">Sign
                In</a>
              <a href="{% url 'account_login' %}" target="_blank"
                 class="btn btn-sm  bg-gradient-primary  btn-round mb-0 me-1 ms-1 mt-2 mt-md-0">Join
                Now</a>
            </div>
          </li>

          {#        <li class="nav-item my-auto ms-3 ms-lg-0">#}
          {##}
          {#            <a href="{% url 'account_login' %}" target="_blank"#}
          {#               class="btn btn-sm  bg-gradient-primary  btn-round mb-0 me-1 mt-2 mt-md-0">Join#}
          {#                Now</a>#}
          {#        </li>#}
          <!-- End Sign In + Sign Up buttons -->
        {% endif %}

      </ul>
    </div>
  </div>
</nav>
<!-- End Navbar -->
