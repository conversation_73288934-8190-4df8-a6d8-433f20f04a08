{% load static %}

<!-- Navbar Light -->
<nav class="navbar navbar-expand-lg navbar-light bg-white py-3 position-sticky z-index-sticky top-0 shadow-none">

  <div class="container g-1 g-lg-4 justify-space-between">
    <!-- START navbar brand for large screens -->
    <a class="navbar-brand font-weight-bold d-none d-lg-block col-4 col-md-3 col-lg-2 me-0 me-lg-1 py-0" href="/" rel="tooltip"
       title="CASHBACK GOAT Saves You More!" data-placement="bottom">
      {#      CASHBACK GOAT#}
      <img src="{% get_media_prefix %}/home/<USER>/cg-logo-2_1.png" class="img-fluid w-80 h-auto p-0 m-0" alt="">
    </a>
    <!-- END navbar brand -->

    <!-- START navbar brand  for small screens-->
    <a class="navbar-brand font-weight-bold d-lg-none col-4 col-md-3 col-lg-1 me-0 me-lg-3 py-0" href="/" rel="tooltip"
       title="CASHBACK GOAT Saves You More!" data-placement="bottom">
      {#      CASHBACK GOAT#}
      <img src="{% get_media_prefix %}/home/<USER>/cg-logo-2_1.png" class="img-fluid w-100 h-auto p-0 m-0" alt="">
    </a>
    <!-- END navbar brand -->

    <!-- START collapsible search bar for small screen -->
    <div class="container col-6 col-md-7 d-block d-lg-none mx-0 px-0">
      <div class="row justify-space-between align-items-center mx-auto py-2">
        <form action="{% url 'search' %}" method="GET" id="search_store" class="form-inline px-0">
          <div class="input-group">
            <span class="input-group-text"><i class="fas fa-search" aria-hidden="true"></i></span>
            <input class="form-control" placeholder="Search Stores and Coupons" name="search_store" type="search">
          </div>
        </form>
      </div>
    </div>
    <!-- END collapsible search bar for small screen -->

    <button class="navbar-toggler shadow-none col-1 col-md-2 ms-0 ms-lg-2" type="button" data-bs-toggle="collapse"
            data-bs-target="#navigation" aria-controls="navigation" aria-expanded="false"
            aria-label="Toggle navigation">
      <span class="navbar-toggler-icon mt-2">
        <span class="navbar-toggler-bar bar1"></span>
        <span class="navbar-toggler-bar bar2"></span>
        <span class="navbar-toggler-bar bar3"></span>
      </span>
    </button>
    <!--END Nav bar buttons for small screens -->


    <!--START Nav bar collapse Items -->
    <div class="collapse navbar-collapse w-100 pt-3 pb-2 py-lg-0" id="navigation">
      <!--END Nav bar collapse Items -->


      <ul class="navbar-nav navbar-nav-hover mx-0 col-lg-12">
        <!--START Stores dropdown-->
        <li class="nav-item dropdown dropdown-hover mx-2 ps-lg-3">
          <a class="nav-link ps-2 d-flex justify-content-between cursor-pointer align-items-center"
             id="dropdownMenuPages6" data-bs-toggle="dropdown" aria-expanded="false" role="button">
            Stores
            <img src="{% static 'assets/img/down-arrow-dark.svg' %}" alt="down-arrow" class="arrow ms-1">
          </a>
          <div class="dropdown-menu dropdown-menu-animation dropdown-lg p-3 border-radius-xl mt-0 mt-lg-3"
               aria-labelledby="dropdownMenuPages6">
            <!--Start Stores - section for Large screens-->
            <div class="d-none d-lg-block">
              <div
                  class="dropdown-header text-dark font-weight-bolder d-flex justify-content-cente align-items-center px-0">
                <div class="d-inline-block">
                  <div
                      class="icon icon-shape icon-xs border-radius-md bg-gradient-primary text-center me-2 d-flex align-items-center justify-content-center">
                    <svg width="12px" height="12px" viewBox="0 0 45 40" version="1.1" xmlns="http://www.w3.org/2000/svg"
                         xmlns:xlink="http://www.w3.org/1999/xlink">
                      <title>shop </title>
                      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <g transform="translate(-1716.000000, -439.000000)" fill="#FFFFFF" fill-rule="nonzero">
                          <g transform="translate(1716.000000, 291.000000)">
                            <g transform="translate(0.000000, 148.000000)">
                              <path
                                  d="M46.7199583,10.7414583 L40.8449583,0.949791667 C40.4909749,0.360605034 39.8540131,0 39.1666667,0 L7.83333333,0 C7.1459869,0 6.50902508,0.360605034 6.15504167,0.949791667 L0.280041667,10.7414583 C0.0969176761,11.0460037 -1.23209662e-05,11.3946378 -1.23209662e-05,11.75 C-0.00758042603,16.0663731 3.48367543,19.5725301 7.80004167,19.5833333 L7.81570833,19.5833333 C9.75003686,19.5882688 11.6168794,18.8726691 13.0522917,17.5760417 C16.0171492,20.2556967 20.5292675,20.2556967 23.494125,17.5760417 C26.4604562,20.2616016 30.9794188,20.2616016 33.94575,17.5760417 C36.2421905,19.6477597 39.5441143,20.1708521 42.3684437,18.9103691 C45.1927731,17.649886 47.0084685,14.8428276 47.0000295,11.75 C47.0000295,11.3946378 46.9030823,11.0460037 46.7199583,10.7414583 Z"
                                  opacity="0.598981585"></path>
                              <path
                                  d="M39.198,22.4912623 C37.3776246,22.4928106 35.5817531,22.0149171 33.951625,21.0951667 L33.92225,21.1107282 C31.1430221,22.6838032 27.9255001,22.9318916 24.9844167,21.7998837 C24.4750389,21.605469 23.9777983,21.3722567 23.4960833,21.1018359 L23.4745417,21.1129513 C20.6961809,22.6871153 17.4786145,22.9344611 14.5386667,21.7998837 C14.029926,21.6054643 13.533337,21.3722507 13.0522917,21.1018359 C11.4250962,22.0190609 9.63246555,22.4947009 7.81570833,22.4912623 C7.16510551,22.4842162 6.51607673,22.4173045 5.875,22.2911849 L5.875,44.7220845 C5.875,45.9498589 6.7517757,46.9451667 7.83333333,46.9451667 L19.5833333,46.9451667 L19.5833333,33.6066734 L27.4166667,33.6066734 L27.4166667,46.9451667 L39.1666667,46.9451667 C40.2482243,46.9451667 41.125,45.9498589 41.125,44.7220845 L41.125,22.2822926 C40.4887822,22.4116582 39.8442868,22.4815492 39.198,22.4912623 Z"></path>
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                  </div>
                </div>
                Shop All Stores
              </div>
              {% for store_category in all_store_categories %}
                <a href="{% url 'store-category' category_id=store_category.id %}"
                   class="dropdown-item border-radius-md">
                  <span class="ps-3">{{ store_category.name }}</span>
                </a>
              {% endfor %}

            </div>
            <!--END Stores - section for Large screens-->

            <!--START Stores - section for Small screens-->
            <div class="d-lg-none">
              <div
                  class="dropdown-header text-dark font-weight-bolder d-flex justify-content-cente align-items-center px-0">
                <div class="d-inline-block">
                  <div
                      class="icon icon-shape icon-xs border-radius-md bg-gradient-primary text-center me-2 d-flex align-items-center justify-content-center">
                    <svg width="12px" height="12px" viewBox="0 0 45 40" version="1.1" xmlns="http://www.w3.org/2000/svg"
                         xmlns:xlink="http://www.w3.org/1999/xlink">
                      <title>shop </title>
                      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <g transform="translate(-1716.000000, -439.000000)" fill="#FFFFFF" fill-rule="nonzero">
                          <g transform="translate(1716.000000, 291.000000)">
                            <g transform="translate(0.000000, 148.000000)">
                              <path
                                  d="M46.7199583,10.7414583 L40.8449583,0.949791667 C40.4909749,0.360605034 39.8540131,0 39.1666667,0 L7.83333333,0 C7.1459869,0 6.50902508,0.360605034 6.15504167,0.949791667 L0.280041667,10.7414583 C0.0969176761,11.0460037 -1.23209662e-05,11.3946378 -1.23209662e-05,11.75 C-0.00758042603,16.0663731 3.48367543,19.5725301 7.80004167,19.5833333 L7.81570833,19.5833333 C9.75003686,19.5882688 11.6168794,18.8726691 13.0522917,17.5760417 C16.0171492,20.2556967 20.5292675,20.2556967 23.494125,17.5760417 C26.4604562,20.2616016 30.9794188,20.2616016 33.94575,17.5760417 C36.2421905,19.6477597 39.5441143,20.1708521 42.3684437,18.9103691 C45.1927731,17.649886 47.0084685,14.8428276 47.0000295,11.75 C47.0000295,11.3946378 46.9030823,11.0460037 46.7199583,10.7414583 Z"
                                  opacity="0.598981585"></path>
                              <path
                                  d="M39.198,22.4912623 C37.3776246,22.4928106 35.5817531,22.0149171 33.951625,21.0951667 L33.92225,21.1107282 C31.1430221,22.6838032 27.9255001,22.9318916 24.9844167,21.7998837 C24.4750389,21.605469 23.9777983,21.3722567 23.4960833,21.1018359 L23.4745417,21.1129513 C20.6961809,22.6871153 17.4786145,22.9344611 14.5386667,21.7998837 C14.029926,21.6054643 13.533337,21.3722507 13.0522917,21.1018359 C11.4250962,22.0190609 9.63246555,22.4947009 7.81570833,22.4912623 C7.16510551,22.4842162 6.51607673,22.4173045 5.875,22.2911849 L5.875,44.7220845 C5.875,45.9498589 6.7517757,46.9451667 7.83333333,46.9451667 L19.5833333,46.9451667 L19.5833333,33.6066734 L27.4166667,33.6066734 L27.4166667,46.9451667 L39.1666667,46.9451667 C40.2482243,46.9451667 41.125,45.9498589 41.125,44.7220845 L41.125,22.2822926 C40.4887822,22.4116582 39.8442868,22.4815492 39.198,22.4912623 Z"></path>
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                  </div>
                </div>
                Shop All Stores
              </div>

              {% for store_category in all_store_categories %}
                <a href="{% url 'store-category' category_id=store_category.id %}"
                   class="dropdown-item border-radius-md">
                  {{ store_category.name }}
                </a>
              {% endfor %}
            </div>
            <!--END Stores - section for Small screens-->
          </div>
        </li>
        <!--END Stores dropdown-->

        <!--START Deals dropdown-->
        <li class="nav-item dropdown dropdown-hover mx-2">
          <a class="nav-link ps-2 d-flex justify-content-between cursor-pointer align-items-center"
             id="dropdownMenuPages6" data-bs-toggle="dropdown" aria-expanded="false" role="button">
            Save
            <img src="{% static 'assets/img/down-arrow-dark.svg' %}" alt="down-arrow" class="arrow ms-1">
          </a>
          <div class="dropdown-menu dropdown-menu-animation dropdown-lg p-3 border-radius-lg mt-0 mt-lg-3"
               aria-labelledby="dropdownMenuPages6">
            <!--Start DEALS - section for Large screens-->
            <div class="d-none d-lg-block">
              <div
                  class="dropdown-header text-dark font-weight-bolder d-flex justify-content-cente align-items-center px-0">
                <div class="d-inline-block">
                  <div
                      class="icon icon-shape icon-xs border-radius-md bg-gradient-primary text-center me-2 d-flex align-items-center justify-content-center ps-0">
                    <svg class="" width="12px" height="20px" viewBox="0 0 40 40" version="1.1"
                         xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                      <title>spaceship</title>
                      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <g transform="translate(-1720.000000, -592.000000)" fill="#FFFFFF" fill-rule="nonzero">
                          <g transform="translate(1716.000000, 291.000000)">
                            <g transform="translate(4.000000, 301.000000)">
                              <path
                                  d="M39.3,0.706666667 C38.9660984,0.370464027 38.5048767,0.192278529 38.0316667,0.216666667 C14.6516667,1.43666667 6.015,22.2633333 5.93166667,22.4733333 C5.68236407,23.0926189 5.82664679,23.8009159 6.29833333,24.2733333 L15.7266667,33.7016667 C16.2013871,34.1756798 16.9140329,34.3188658 17.535,34.065 C17.7433333,33.98 38.4583333,25.2466667 39.7816667,1.97666667 C39.8087196,1.50414529 39.6335979,1.04240574 39.3,0.706666667 Z M25.69,19.0233333 C24.7367525,19.9768687 23.3029475,20.2622391 22.0572426,19.7463614 C20.8115377,19.2304837 19.9992882,18.0149658 19.9992882,16.6666667 C19.9992882,15.3183676 20.8115377,14.1028496 22.0572426,13.5869719 C23.3029475,13.0710943 24.7367525,13.3564646 25.69,14.31 C26.9912731,15.6116662 26.9912731,17.7216672 25.69,19.0233333 L25.69,19.0233333 Z"></path>
                              <path
                                  d="M1.855,31.4066667 C3.05106558,30.2024182 4.79973884,29.7296005 6.43969145,30.1670277 C8.07964407,30.6044549 9.36054508,31.8853559 9.7979723,33.5253085 C10.2353995,35.1652612 9.76258177,36.9139344 8.55833333,38.11 C6.70666667,39.9616667 0,40 0,40 C0,40 0,33.2566667 1.855,31.4066667 Z"></path>
                              <path
                                  d="M17.2616667,3.90166667 C12.4943643,3.07192755 7.62174065,4.61673894 4.20333333,8.04166667 C3.31200265,8.94126033 2.53706177,9.94913142 1.89666667,11.0416667 C1.5109569,11.6966059 1.61721591,12.5295394 2.155,13.0666667 L5.47,16.3833333 C8.55036617,11.4946947 12.5559074,7.25476565 17.2616667,3.90166667 L17.2616667,3.90166667 Z"
                                  opacity="0.598539807"></path>
                              <path
                                  d="M36.0983333,22.7383333 C36.9280725,27.5056357 35.3832611,32.3782594 31.9583333,35.7966667 C31.0587397,36.6879974 30.0508686,37.4629382 28.9583333,38.1033333 C28.3033941,38.4890431 27.4704606,38.3827841 26.9333333,37.845 L23.6166667,34.53 C28.5053053,31.4496338 32.7452344,27.4440926 36.0983333,22.7383333 L36.0983333,22.7383333 Z"
                                  opacity="0.598539807"></path>
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                  </div>
                </div>
                Extra Money Saver
              </div>
              <a href="{% url 'posts-all' %}" class="dropdown-item border-radius-md">
                <span class="ps-3">Deals</span>
              </a>
              <a href="{% url 'articles-all' %}" class="dropdown-item border-radius-md">
                <span class="ps-3">Buying Guide <span class="badge badge-sm bg-gradient-dark ms-1">NEW</span></span>
              </a>
              <a href="" class="dropdown-item border-radius-md">
                <span class="ps-3">Coupons <span class="badge badge-sm bg-gradient-dark ms-1">SOON</span></span>
              </a>
            </div>
            <!--END DEALS - section for Large screens-->

            <!--START DEALS - section for Small screens-->
            <div class="d-lg-none">
              <div
                  class="dropdown-header text-dark font-weight-bolder d-flex justify-content-cente align-items-center px-0">
                <div class="d-inline-block">
                  <div
                      class="icon icon-shape icon-xs border-radius-md bg-gradient-primary text-center me-2 d-flex align-items-center justify-content-center">
                    <svg width="12px" height="12px" viewBox="0 0 45 40" version="1.1" xmlns="http://www.w3.org/2000/svg"
                         xmlns:xlink="http://www.w3.org/1999/xlink">
                      <title>shop </title>
                      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <g transform="translate(-1716.000000, -439.000000)" fill="#FFFFFF" fill-rule="nonzero">
                          <g transform="translate(1716.000000, 291.000000)">
                            <g transform="translate(0.000000, 148.000000)">
                              <path
                                  d="M46.7199583,10.7414583 L40.8449583,0.949791667 C40.4909749,0.360605034 39.8540131,0 39.1666667,0 L7.83333333,0 C7.1459869,0 6.50902508,0.360605034 6.15504167,0.949791667 L0.280041667,10.7414583 C0.0969176761,11.0460037 -1.23209662e-05,11.3946378 -1.23209662e-05,11.75 C-0.00758042603,16.0663731 3.48367543,19.5725301 7.80004167,19.5833333 L7.81570833,19.5833333 C9.75003686,19.5882688 11.6168794,18.8726691 13.0522917,17.5760417 C16.0171492,20.2556967 20.5292675,20.2556967 23.494125,17.5760417 C26.4604562,20.2616016 30.9794188,20.2616016 33.94575,17.5760417 C36.2421905,19.6477597 39.5441143,20.1708521 42.3684437,18.9103691 C45.1927731,17.649886 47.0084685,14.8428276 47.0000295,11.75 C47.0000295,11.3946378 46.9030823,11.0460037 46.7199583,10.7414583 Z"
                                  opacity="0.598981585"></path>
                              <path
                                  d="M39.198,22.4912623 C37.3776246,22.4928106 35.5817531,22.0149171 33.951625,21.0951667 L33.92225,21.1107282 C31.1430221,22.6838032 27.9255001,22.9318916 24.9844167,21.7998837 C24.4750389,21.605469 23.9777983,21.3722567 23.4960833,21.1018359 L23.4745417,21.1129513 C20.6961809,22.6871153 17.4786145,22.9344611 14.5386667,21.7998837 C14.029926,21.6054643 13.533337,21.3722507 13.0522917,21.1018359 C11.4250962,22.0190609 9.63246555,22.4947009 7.81570833,22.4912623 C7.16510551,22.4842162 6.51607673,22.4173045 5.875,22.2911849 L5.875,44.7220845 C5.875,45.9498589 6.7517757,46.9451667 7.83333333,46.9451667 L19.5833333,46.9451667 L19.5833333,33.6066734 L27.4166667,33.6066734 L27.4166667,46.9451667 L39.1666667,46.9451667 C40.2482243,46.9451667 41.125,45.9498589 41.125,44.7220845 L41.125,22.2822926 C40.4887822,22.4116582 39.8442868,22.4815492 39.198,22.4912623 Z"></path>
                            </g>
                          </g>
                        </g>
                      </g>
                    </svg>
                  </div>
                </div>
                Extra Money Saver
              </div>
              <a href="{% url 'posts-all' %}" class="dropdown-item border-radius-md">
                Deals & Specials <span class="badge badge-sm bg-gradient-dark ms-1">NEW</span>
              </a>
              <a href="" class="dropdown-item border-radius-md">
                Coupons <span class="badge badge-sm bg-gradient-dark ms-1">SOON</span>
              </a>
            </div>
            <!--END DEALS - section for Small screens-->
          </div>
        </li>
        <!--END Deals dropdown-->

        <!--Start Search bar for med-xl screens-->
        {#       mx-2 ps-lg-2#}
        <li class="nav-item dropdown dropdown-hover mx-auto pe-lg-6 d-none d-lg-block">

          <div class="row text-center py-0 mt-auto mx-auto" id="search-box">
            <div class="col-auto mx-auto">
              <div class="input-group mx-xl-0 position-relative">
                <form action="{% url 'search' %}" method="GET" id="search_store" class="form-inline">
                  <input class="form-control" placeholder="Search Stores and Coupons" type="search" name="search_store"
                         size="65">
                </form>
                {#                <button type="submit" form="search_store" class="btn btn-primary w-auto me-0 mb-0"#}
                {#                        style="position:absolute;top:0px;right:0px;"><i class="fas fa-search" aria-hidden="true"></i>#}
                {#                </button>#}

                <button type="submit" form="search_store" class="btn btn-primary w-auto me-0 mb-0"
                        style="position: absolute;top: 0rem;right: 0rem;"><i class="fas fa-search"
                                                                             aria-hidden="true"></i>
                </button>

              </div>

            </div>
          </div>
        </li>
        <!--END Search bar for med-xl screens-->



        {% if request.user.is_authenticated %}
          <!--START Account dropdown-->
          <li class="nav-item dropdown dropdown-hover mx-2 ms-lg-auto">
            <a class="nav-link ps-2 d-flex justify-content-between cursor-pointer align-items-center"
               id="dropdownMenuAccount" data-bs-toggle="dropdown" aria-expanded="false" role="button">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                   class="pe-1 d-none d-lg-flex" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round"
                   stroke-linejoin="round">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
              </svg>
              {{ user.get_username | capfirst }}
              <img src="{% static 'assets/img/down-arrow-dark.svg' %}" alt="down-arrow" class="arrow ms-1">
            </a>
            <div class="dropdown-menu dropdown-menu-animation dropdown-lg border-radius-xl p-3 mt-0 mt-lg-3"
                 aria-labelledby="dropdownMenuAccount">
              <!--START Account dropdown for med-xl screens-->
              <div class="row d-none d-lg-flex">
                <div class="col-6">
                  <div class="py-6 h-100 w-100 d-flex border-radius-lg position-relative dropdown-image"
                       style="background-image:url('{% static 'assets/img/curved-images/curved8.jpg' %}')">
                    <div class="mask bg-gradient-primary border-radius-lg"></div>
                    <div
                        class="d-flex justify-content-center align-items-center text-center text-white font-weight-bold w-100 z-index-1 flex-column">
                      <div class="icon icon-shape rounded-circle bg-white shadow text-center">
                        <i class="ni ni-diamond text-primary text-gradient text-lg" aria-hidden="true"></i>
                      </div>
                      <span class="text-lg mt-2">Refer and <br>Earn More!</span>
                    </div>
                  </div>
                </div>
                <div class="col-6 ps-0 d-flex justify-content-center flex-column">
                  <ul class="list-group">
                    <li class="nav-item list-group-item border-0 p-0">
                      <a href=""
                         class="dropdown-item border-radius-md ps-3 d-flex align-items-center justify-content-between mb-1">
                        <span>My Account</span>
                      </a>
                    </li>
                    <li class="nav-item list-group-item border-0 p-0">
                      <a href=""
                         class="dropdown-item border-radius-md ps-3 d-flex align-items-center justify-content-between mb-1">
                        <span>Favorite Stores</span>
                      </a>
                    </li>
                    <li class="nav-item list-group-item border-0 p-0">
                      <a href=""
                         class="dropdown-item border-radius-md ps-3 d-flex align-items-center justify-content-between mb-1">
                        <span>My Referrals</span>
                      </a>
                    </li>
                    <li class="nav-item list-group-item border-0 p-0">
                      <a href=""
                         class="dropdown-item border-radius-md ps-3 d-flex align-items-center justify-content-between mb-1">
                        <span>Cash Balance</span>
                      </a>
                    </li>
                    <li class="nav-item list-group-item border-0 p-0">
                      <a href=""
                         class="dropdown-item border-radius-md ps-3 d-flex align-items-center justify-content-between mb-1">
                        <span>Click Summary</span>
                      </a>
                    </li>
                    <li class="nav-item list-group-item border-0 p-0">
                      <a href=""
                         class="dropdown-item border-radius-md ps-3 d-flex align-items-center justify-content-between mb-1">
                        <span>Account Settings</span>
                      </a>
                    </li>
                    <li class="nav-item list-group-item border-0 p-0">
                      <a href=""
                         class="dropdown-item border-radius-md ps-3 d-flex align-items-center justify-content-between mb-1">
                        <span>Help</span>
                      </a>
                    </li>
                    {% if request.user.is_authenticated %}
                      <li class="nav-item list-group-item border-0 p-0">
                        <a href="{% url 'account_logout' %}"
                           class="dropdown-item border-radius-md ps-3 d-flex align-items-center mb-1">
                          <span>Logout</span>
                        </a>
                      </li>
                    {% endif %}
                  </ul>
                </div>
              </div>
              <!--END Account dropdown for med-xl screens-->

              <!--START Account dropdown for small screens-->
              <div class="row d-lg-none">
                <div class="col-12 d-flex justify-content-center flex-column">
                  <a href="" class="dropdown-item border-radius-md">
                    My Account
                  </a>
                  <a href="" class="dropdown-item border-radius-md">
                    Favorite Stores
                  </a>
                  <a href="" class="dropdown-item border-radius-md">
                    My Referrals
                  </a>
                  <a href="" class="dropdown-item border-radius-md">
                    Cash Balance
                  </a>
                  <a href="" class="dropdown-item border-radius-md">
                    Click Summary
                  </a>
                  <a href="" class="dropdown-item border-radius-md">
                    Account Settings
                  </a>
                  <a href="" class="dropdown-item border-radius-md">
                    Help
                  </a>
                  {% if request.user.is_authenticated %}
                    <a href="{% url 'account_logout' %}" class="dropdown-item border-radius-md">
                      Sign Out
                    </a>
                  {% endif %}
                </div>
              </div>
              <!--END Account dropdown for med-xl screens-->
            </div>
          </li>
          <!--END Account dropdown-->
        {% else %}
          <!-- START Sign In + Sign Up buttons for LARGE screens -->
          {#          <li class="nav-item mx-2 ms-lg-auto d-none d-lg-block">#}
          {#            <a href="{% url 'account_login' %}" target="_blank"#}
          {#               class="btn btn-sm btn-outline-primary btn-round my-lg-1 me-1">Sign In</a>#}
          {#          </li>#}
          {#          <li class="nav-item mx-2 me-0 d-none d-lg-block">#}
          {#            <a href="{% url 'account_login' %}" target="_blank"#}
          {#               class="btn btn-sm bg-gradient-primary btn-round my-lg-1 me-1">Join Now</a>#}
          {#          </li>#}

          <li class="nav-item mx-2 ms-lg-auto d-none d-lg-block">
            <a href="{% url 'account_login' %}" target="_blank"
               class="btn btn-sm bg-gradient-primary btn-round my-lg-1 me-1">Sign In</a>
          </li>

          <!-- END Sign In + Sign Up buttons for LARGE screens  -->

          <!-- START Sign In + Sign Up buttons for SMALL screens -->
          <div class="container d-block d-lg-none px-0">
            <div class="row justify-content-evenly  py-0 mt-auto mx-0">
              <a href="{% url 'account_login' %}" target="_blank"
                 class="btn btn-sm btn-outline-primary btn-round my-lg-1 mx-2 col-4">Sign In</a>
              <a href="{% url 'account_login' %}" target="_blank"
                 class="btn btn-sm bg-gradient-primary btn-round my-lg-1 mx-2 col-4">Join Now</a>
            </div>
          </div>
          <!-- END Sign In + Sign Up buttons for SMALL screens  -->


        {% endif %}

      </ul>


      {#      {% if not request.user.is_authenticated %}#}
      {#      <!-- START Sign In + Sign Up buttons for LARGE screens -->#}
      {#        <ul class="navbar-nav d-lg-block d-none">#}
      {#          <div class="d-flex justify-content-cente align-items-center px-0">#}
      {#          <li class="nav-item">#}
      {#            <a href="{% url 'account_login' %}" target="_blank"#}
      {#               class="btn btn-sm  bg-outline-primary  btn-round mb-0 me-1">Sign In</a>#}
      {#          </li>#}
      {#          <li class="nav-item">#}
      {#            <a href="{% url 'account_login' %}" target="_blank"#}
      {#               class="btn btn-sm  bg-gradient-primary  btn-round mb-0 me-1">Join Now</a>#}
      {#          </li>#}
      {#          </div>#}
      {#        </ul>#}
      {#      <!-- END Sign In + Sign Up buttons for LARGE screens  -->#}
      {#      {% endif %}#}


    </div>


  </div>
</nav>
<!-- End Navbar -->