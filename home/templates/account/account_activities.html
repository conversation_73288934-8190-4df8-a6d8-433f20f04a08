{% extends 'account/account_base.html' %}

{% load static %}

{% block section_title %}Cashback Activities{% endblock %}

{% block account_content %}
    <!-- Page specific content here -->
    <div class="row">

        <!-- Recent Activities Tables -->
        <div class="col-12 mt-4">
            <!-- Recent Click Activities -->
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <div class="d-flex align-items-center">
                        <h6 class="mb-0">Recent Store Visits</h6>
                        <a href="/" class="btn btn-primary btn-sm ms-auto">View All</a>
                    </div>
                </div>
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                            <tr>
                                <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Date</th>
                                <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Store</th>
                                <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Status</th>
                                <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Click ID</th>
                            </tr>
                            </thead>
                            <tbody>
                            {% for click in user.userprofile.get_recent_clicks %}
                                <tr>
                                    <td>
                                        <div class="d-flex px-3 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm">{{ click.created_at|date:"M d, Y" }}</h6>
                                                <p class="text-xs text-secondary mb-0">{{ click.created_at|time:"g:i A" }}</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <p class="text-sm font-weight-bold mb-0">
                                            {% with store=click.get_store %}
                                                {{ store.name|default:"Unknown Store" }}
                                            {% endwith %}
                                        </p>
                                    </td>
                                    <td>
                                        {% if click.status == 'NEW' %}
                                            <span class="badge badge-sm bg-gradient-info">New</span>
                                        {% elif click.status == 'COMPLETED' %}
                                            <span class="badge badge-sm bg-gradient-success">Completed</span>
                                        {% else %}
                                            <span class="badge badge-sm bg-gradient-secondary">{{ click.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="text-xs">#{{ click.id }}</span>
                                    </td>
                                </tr>
                            {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center py-4">
                                        <p class="text-sm mb-0">No recent store visits.</p>
                                    </td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Recent Earnings -->
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <div class="d-flex align-items-center">
                        <h6 class="mb-0">Recent Earnings</h6>
                        <a href="/" class="btn btn-primary btn-sm ms-auto">View All</a>
                    </div>
                </div>
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                            <tr>
                                <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Date</th>
                                <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Type</th>
                                <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Store</th>
                                <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Status</th>
                                <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Amount</th>
                            </tr>
                            </thead>
                            <tbody>
                            {% for earning in user.userprofile.get_recent_earnings %}
                                <tr>
                                    <td>
                                        <div class="d-flex px-3 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm">{{ earning.created_at|date:"M d, Y" }}</h6>
                                                <p class="text-xs text-secondary mb-0">{{ earning.created_at|time:"g:i A" }}</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <p class="text-sm font-weight-bold mb-0">{{ earning.get_earning_type_display }}</p>
                                    </td>
                                    <td>
                                        <p class="text-sm font-weight-bold mb-0">{{ earning.store.name|default:"-" }}</p>
                                    </td>
                                    <td>
                                        {% if earning.status == 'PE' %}
                                            <span class="badge badge-sm bg-gradient-warning">Pending</span>
                                        {% elif earning.status == 'AP' %}
                                            <span class="badge badge-sm bg-gradient-success">Approved</span>
                                        {% elif earning.status == 'RE' %}
                                            <span class="badge badge-sm bg-gradient-danger">Rejected</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="text-sm font-weight-bold">${{ earning.amount|floatformat:2 }}</span>
                                    </td>
                                </tr>
                            {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <p class="text-sm mb-0">No recent earnings.</p>
                                    </td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Recent Payouts -->
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <div class="d-flex align-items-center">
                        <h6 class="mb-0">Recent Payouts</h6>
                        <a href="/" class="btn btn-primary btn-sm ms-auto">View All</a>
                    </div>
                </div>
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                            <tr>
                                <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Date</th>
                                <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Payment Method</th>
                                <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Status</th>
                                <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Amount</th>
                            </tr>
                            </thead>
                            <tbody>
                            {% for payout in user.userprofile.get_recent_payouts %}
                                <tr>
                                    <td>
                                        <div class="d-flex px-3 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm">{{ payout.created_at|date:"M d, Y" }}</h6>
                                                <p class="text-xs text-secondary mb-0">{{ payout.created_at|time:"g:i A" }}</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <p class="text-sm font-weight-bold mb-0">{{ payout.payment_method.get_method_type_display }}</p>
                                    </td>
                                    <td>
                                        {% if payout.status == 'PE' %}
                                            <span class="badge badge-sm bg-gradient-warning">Pending</span>
                                        {% elif payout.status == 'PR' %}
                                            <span class="badge badge-sm bg-gradient-info">Processing</span>
                                        {% elif payout.status == 'CO' %}
                                            <span class="badge badge-sm bg-gradient-success">Completed</span>
                                        {% elif payout.status == 'FA' %}
                                            <span class="badge badge-sm bg-gradient-danger">Failed</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="text-sm font-weight-bold">${{ payout.amount|floatformat:2 }}</span>
                                    </td>
                                </tr>
                            {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center py-4">
                                        <p class="text-sm mb-0">No recent payouts.</p>
                                    </td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Recent Activities Tables -->


    </div>
{% endblock %}