{% extends 'layouts/base.html' %}
{% load static %}

{% block title %}Cashback Goat - Get Exclusive Cashback & Maximize Your Savings{% endblock title %}
{% block meta-description %}Earn cashback on your everyday shopping with Cashback Goat. Browse over 3,500+ stores offering exclusive cashback deals, coupons, and discounts to maximize your savings.{% endblock %}

<!-- Add FAQ schema for common questions -->
{% block additional-schema %}
    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": [
                {
                    "@type": "Question",
                    "name": "How does Cashback Goat work?",
                    "acceptedAnswer": {
                        "@type": "Answer",
                        "text": "Simply browse our site for your favorite stores, click through to the retailer, and shop normally. We'll track your purchase and credit your account with the earned cashback."
                    }
                },
                {
                    "@type": "Question",
                    "name": "How much cashback can I earn?",
                    "acceptedAnswer": {
                        "@type": "Answer",
                        "text": "Cashback rates vary by store, ranging from 1% to over 40% on select retailers."
                    }
                },
                {
                    "@type": "Question",
                    "name": "When will I receive my cashback?",
                    "acceptedAnswer": {
                        "@type": "Answer",
                        "text": "Cashback typically appears as pending within 14 days and becomes available for withdrawal after the retailer's return period (usually 30-90 days)."
                    }
                }
            ]
        }
    </script>
{% endblock %}

{% block extrastyle %}
    {{ block.super }}
    <!-- Mobile optimization for featured cards -->
    <style>
        @media (max-width: 767.98px) {
            .featured-cards-container {
                display: flex;
                overflow-x: auto;
                scroll-snap-type: x mandatory;
                -webkit-overflow-scrolling: touch;
                scrollbar-width: none; /* Firefox */
                padding-bottom: 10px;
                margin: 0 -12px; /* Negative margin to offset container padding */
                padding: 0 12px; /* Add padding back to the sides */
            }

            .featured-cards-container::-webkit-scrollbar {
                display: none; /* Chrome, Safari, Opera */
            }

            .featured-cards-container .featured-card {
                flex: 0 0 85%;
                scroll-snap-align: start;
                margin-right: 15px;
            }

            .featured-cards-container .featured-card:last-child {
                margin-right: 0;
            }

            /* Indicator dots for the scrollable cards */
            .scroll-indicator {
                display: flex;
                justify-content: center;
                margin-top: 10px;
            }

            .scroll-indicator .dot {
                height: 8px;
                width: 8px;
                margin: 0 4px;
                background-color: #bbb;
                border-radius: 50%;
                display: inline-block;
                transition: background-color 0.3s ease;
                cursor: pointer;
            }

            .scroll-indicator .dot.active {
                background-color: #717171;
                transform: scale(1.2);
            }
        }

        /* Custom column class for 8 stores per row */
        @media (min-width: 992px) {
            .col-lg-8-stores {
                flex: 0 0 auto;
                width: 12.5%; /* 100% ÷ 8 = 12.5% */
            }
        }
    </style>

    <!-- Replacing custom CSS with Soft UI Design System classes -->
    <style>
        /* Mobile optimization for featured cards */
        @media (max-width: 767.98px) {
            .featured-cards-container {
                display: flex;
                overflow-x: auto;
                scroll-snap-type: x mandatory;
                -webkit-overflow-scrolling: touch;
                scrollbar-width: none; /* Firefox */
                padding-bottom: 10px;
                margin: 0 -12px; /* Negative margin to offset container padding */
                padding: 0 12px; /* Add padding back to the sides */
            }

            .featured-cards-container::-webkit-scrollbar {
                display: none; /* Chrome, Safari, Opera */
            }

            .featured-cards-container .featured-card {
                flex: 0 0 85%;
                scroll-snap-align: start;
                margin-right: 15px;
            }

            .featured-cards-container .featured-card:last-child {
                margin-right: 0;
            }

            /* Indicator dots for the scrollable cards */
            .scroll-indicator {
                display: flex;
                justify-content: center;
                margin-top: 10px;
            }

            .scroll-indicator .dot {
                height: 8px;
                width: 8px;
                margin: 0 4px;
                background-color: #bbb;
                border-radius: 50%;
                display: inline-block;
                transition: background-color 0.3s ease;
                cursor: pointer;
            }

            .scroll-indicator .dot.active {
                background-color: #717171;
                transform: scale(1.2);
            }
        }

        /* Store scroll containers */
        .store-scroll-container::-webkit-scrollbar,
        .stats-scroll-container::-webkit-scrollbar {
            display: none;
        }

        /* Store item styling for mobile */
        .store-item {
            display: inline-block;
            width: 75px;
            margin-right: 7px;
            scroll-snap-align: start;
            vertical-align: top;
        }

        /* Make store rows horizontally scrollable */
        .store-scroll-row {
            display: flex;
            flex-wrap: nowrap;
            overflow-x: hidden;
            scroll-behavior: smooth;
            scroll-snap-type: x mandatory;
        }

        .store-scroll-row .col-lg-2 {
            flex: 0 0 auto;
            scroll-snap-align: start;
        }

        /* Store logo hover effect */
        .move-on-hover {
            transition: transform 0.2s ease;
        }

        .move-on-hover:hover {
            transform: translateY(-3px);
        }

        /* Micro navigation styling using Soft UI classes */
        .nav-micro .nav-link {
            color: var(--bs-secondary);
            font-size: 0.75rem;
            transition: all 0.15s ease;
            opacity: 0.7;
        }

        .nav-micro .nav-link:hover {
            opacity: 1;
            color: var(--bs-dark);
        }

        .nav-micro .nav-link.active {
            color: var(--bs-primary);
            opacity: 1;
        }
    </style>
{% endblock extrastyle %}

{#{% block body %}index-page bg-gray-200{% endblock body %}#}

{% block content %}

    <!-- -------- START Featured Section ------- -->
    <section class="pt-2 pb-2 pb-md-4">
        <div class="container px-1">
            <!-- Desktop version - visible only on medium screens and up -->
            <div class="row g-3 d-none d-md-flex">
                <!-- Main Featured Content -->
                <div class="col-12 col-lg-8">

                    {% if user.is_authenticated %}
                        <!-- Content for Signed-in Users -->
                        <div class="card card-body blur text-md-start text-center px-sm-5 shadow-lg py-sm-3 bg-gradient-primary  overflow-hidden h-100" style="background-image: linear-gradient(145deg, #cb0d9f 0%, #5627ff 100%);">
                            <div class="row align-items-center">
                                <div class="col-lg-7">
                                    <div class="d-flex align-items-center mb-3">
                                        <span class="badge bg-danger me-2">Limited Time</span>
                                        {#                                        <span class="text-white small">Limited Time Offer</span>#}
                                    </div>

                                    <h2 class="text-white mb-0">Extra 10% Cash Back</h2>
                                    <h2 class="text-white mb-4">at Temu Today</h2>

                                    {#                                <span class="text-dark h2 mb-2">at </span><span class="text-primary h2 mb-2">Temu</span><span class="text-dark h2 mb-2"> Today</span>#}
                                    <p class="text-white font-italic pe-md-5 me-md-5">
                                        Grab while it lasts!*
                                    </p>
                                    <div class="buttons">
                                        <a href="{% url 'store' store_slug='temu' %}"
                                           target="_blank"
                                           rel="noopener"
                                           class="btn btn-lg btn-rounded bg-white mt-2">
                                            Shop Now
                                        </a>
                                    </div>
                                </div>

                                <!-- logo area -->
                                <div class="col-lg-5 d-none d-md-block d-md-flex align-items-center justify-content-center">
                                    <div class="position-relative" style="width: 180px; height: 180px;">
                                        <!-- White circular background with shadow -->
                                        <div class="position-absolute top-50 start-50 translate-middle rounded-circle bg-white shadow-lg d-flex align-items-center justify-content-center" style="width: 180px; height: 180px;">
                                            <img src="{% get_media_prefix %}home/store/icon/web/<EMAIL>"
                                                 style="width: 120px; height: 120px; object-fit: contain;"
                                                 alt="Store Logo"
                                                 class="transition-all">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    {% else %}
                        <!-- Content for Visitors - Keep existing visitor content -->
                        <div class="card card-body blur text-md-start text-center px-sm-5 shadow-lg py-sm-3 bg-gradient-primary  overflow-hidden h-100" style="background-image: linear-gradient(145deg, #cb0d9f 0%, #5627ff 100%);">
                            <div class="row align-items-center">
                                <div class="col-lg-7">
                                    <div class="d-flex align-items-center mb-3">
                                        <span class="badge bg-danger me-2">New Members</span>
                                    </div>

                                    <h1 class="text-white h2 fw-bold mb-3">Get $10 Bonus <span class="text-white-50 font-weight-lighter">+</span><br>



                                        <span class="text-white-50 font-weight-lighter">up to </span><span class="display-5">40% </span><span class="display-7">Cash Back</span>
                                    </h1>

                                    <div class="d-flex gap-3 mb-4">
                                        <div class="d-flex align-items-center text-white">
                                            <i class="fas fa-check-circle me-2"></i>
                                            <span>3,500+ Stores</span>
                                        </div>
                                        <div class="d-flex align-items-center text-white">
                                            <i class="fas fa-users me-2"></i>
                                            <span>5-start Ratings</span>
                                        </div>
                                    </div>

                                    <div class="d-flex align-items-center gap-3">
                                        <a href="{% url 'account_signup' %}" class="btn btn-lg btn-white">
                                            Join Now
                                            {#                                                    <i class="fas fa-arrow-right ms-2"></i>#}
                                        </a>
                                        <span class="text-white-50 small">No Credit Card Required</span>
                                    </div>

                                </div>

                                <!-- logo area -->
                                <div class="col-lg-5 d-none d-md-block d-md-flex align-items-center justify-content-center">
                                    <div class="position-relative text-center">
                                        <div class="position-absolute top-50 start-50 translate-middle" style="z-index: 1;">
                                            <svg width="200" height="200" viewBox="0 0 100 100">
                                                <circle cx="50" cy="50" r="45" fill="rgba(255,255,255,0.1)"/>
                                                <circle cx="50" cy="50" r="35" fill="rgba(255,255,255,0.05)"/>
                                            </svg>
                                        </div>
                                        <div class="position-relative" style="z-index: 2;">
                                            <i class="fas fa-shopping-bag text-white mb-3" style="font-size: 64px;"></i>
                                            <div class="d-flex justify-content-center gap-2">
                                                <div class="bg-success text-white rounded-pill px-3 py-2">
                                                    <i class="fas fa-percentage me-1"></i>
                                                    Cashback
                                                </div>
                                                <div class="bg-warning text-dark rounded-pill px-3 py-2">
                                                    <i class="fas fa-tag me-1"></i>
                                                    Deals
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    {% endif %}
                </div>

                <!-- Side Thumbnails -->
                <div class="col-12 col-lg-4">
                    <div class="row g-3 h-100">
                        <!-- Thumbnail 1 - Hot Deals -->
                        <div class="col-sm-6 col-lg-12" style="height: calc(50% - 0.5rem);">
                            <a href="{% url 'store' store_slug='durango-boots' %}" class="card border-0 shadow-sm text-decoration-none overflow-hidden h-100">
                                <div class="position-relative h-100">
                                    <!-- Content -->
                                    <div class="position-relative h-100 d-flex align-items-center px-4" style="background-image: linear-gradient(145deg, #2152ff 0%, #21d4fd 100%);">
                                        <!-- Icon Circle -->
                                        <div class="bg-white rounded-circle d-flex align-items-center justify-content-center" style="width: 56px; height: 56px; flex-shrink: 0;">
                                            <img src="https://ik.imagekit.io/v4piwnuuh/cashback_goat/media/home/<USER>/logo/durangoboots-mark-fullcolor-14392_4x.png"
                                                 style="width: 52px; height: 52px; object-fit: contain;"
                                                 alt="Durango Boots Father's Day Sales">
                                        </div>
                                        <!-- Text Content -->
                                        <div class="ms-3 flex-grow-1">
                                            <div class="d-flex align-items-center gap-2 mb-1">
                                                <span class="badge bg-danger text-white small py-1">Trending</span>
                                            </div>
                                            <div class="d-flex align-items-baseline gap-1">
                                                <h3 class="text-white mb-0 h7">Boots for Dad</h3>
                                            </div>
                                            <p class="text-white mb-0">
                                                <span class="text-xs">Shop </span>
                                                <span class="h6 text-white">Durango's</span><br>
                                                <span class="small">Father's Day Sales & Coupons</span>
                                            </p>
                                        </div>
                                        <!-- Arrow -->
                                        <div class="ms-2">
                                            <i class="fas fa-arrow-right text-white-50" style="font-size: 20px;"></i>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>

                        <!-- Thumbnail 2 - New Store -->
                        <div class="col-sm-6 col-lg-12" style="height: calc(50% - 0.5rem);">
                            <a href="{% url 'store' store_slug='ebay' %}" class="card border-0 shadow-sm text-decoration-none overflow-hidden h-100">
                                <div class="position-relative h-100">
                                    <!-- Content -->
                                    <div class="position-relative h-100 d-flex align-items-center px-4" style="background: linear-gradient(145deg, #1a1f37 0%, #212529 100%);">
                                        <!-- Icon Circle -->
                                        <div class="bg-white rounded-circle d-flex align-items-center justify-content-center" style="width: 56px; height: 56px; flex-shrink: 0;">
                                            <img src="https://ik.imagekit.io/v4piwnuuh/cashback_goat/media/home/<USER>/logo/ebay-mark-fullcolor-3993_4x.png"
                                                 style="width: 32px; height: 32px; object-fit: contain;"
                                                 alt="ebay sales">
                                        </div>
                                        <!-- Text Content -->
                                        <div class="ms-3 flex-grow-1">
                                            <div class="d-flex align-items-center gap-2 mb-1">
                                                <span class="badge bg-success text-white small py-1">Ending Soon</span>
                                            </div>
                                            <div class="d-flex align-items-baseline gap-1">
                                                <h3 class="text-white mb-0 h2">Ebay Week</h3>
                                            </div>
                                            <p class="text-white mb-0">
                                                <span class="text-xs">Up to</span>
                                                <span class="h4 text-white mb-0">40% </span>
                                                <span class="small">Extra Cashback</span>
                                            </p>
                                        </div>
                                        <!-- Arrow -->
                                        <div class="ms-2">
                                            <i class="fas fa-arrow-right text-white-50" style="font-size: 20px;"></i>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mobile version - visible only on small screens -->
            <div class="d-md-none">
                <!-- Using Soft UI carousel component -->
                <div id="featuredCarousel" class="carousel slide" data-bs-ride="carousel">
                    <div class="carousel-indicators mb-1">
                        <button type="button" data-bs-target="#featuredCarousel" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
                        <button type="button" data-bs-target="#featuredCarousel" data-bs-slide-to="1" aria-label="Slide 2"></button>
                        <button type="button" data-bs-target="#featuredCarousel" data-bs-slide-to="2" aria-label="Slide 3"></button>
                    </div>
                    <div class="carousel-inner">
                        <!-- Main Featured Card -->
                        <div class="carousel-item active">
                            {% if user.is_authenticated %}
                                <!-- Content for Signed-in Users -->
                                <div class="card shadow-lg overflow-hidden" style="height: 220px;">
                                    <div class="card-body d-flex flex-column justify-content-center p-4 bg-gradient-primary h-100" style="background-image: linear-gradient(145deg, #cb0d9f 0%, #5627ff 100%);">
                                        <div class="text-center">
                                            <div class="d-flex align-items-center justify-content-center mb-2">
                                                <span class="badge bg-danger me-2">Limited Time</span>
                                            </div>

                                            <h3 class="text-white mb-0">Extra 15% Cash Back</h3>
                                            <h4 class="text-white mb-3">at Temu Today</h4>

                                            <div class="buttons">
                                                <a href="{% url 'store' store_slug='temu' %}"
                                                   target="_blank"
                                                   rel="noopener"
                                                   class="btn btn-sm btn-rounded bg-white mt-2">
                                                    Shop Now
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% else %}
                                <!-- Content for Visitors - Simplified for mobile -->
                                <div class="card shadow-lg overflow-hidden" style="height: 220px;">
                                    <div class="card-body d-flex flex-column justify-content-center p-4 bg-gradient-primary h-100" style="background-image: linear-gradient(145deg, #cb0d9f 0%, #5627ff 100%);">
                                        <div class="text-center">
                                            <div class="d-flex align-items-center justify-content-center mb-2">
                                                <span class="badge bg-danger me-2">New Members</span>
                                            </div>

                                            <h3 class="text-white fw-bold mb-2">Get $10 Bonus <span class="text-white-50 font-weight-lighter">+</span></h3>
                                            <h4 class="text-white mb-3"><span class="text-white-50 font-weight-lighter">up to </span>40% Cash Back</h4>

                                            <div class="d-flex justify-content-center gap-3 mb-3">
                                                <a href="{% url 'account_signup' %}" class="btn btn-sm btn-white">
                                                    Join Now
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}
                        </div>

                        <!-- 2nd Card - Durango Boots -->
                        <div class="carousel-item">
                            <div class="card shadow-lg overflow-hidden" style="height: 220px;">
                                <div class="card-body d-flex flex-column justify-content-center p-4 bg-gradient-primary h-100" style="background-image: linear-gradient(145deg, #2152ff 0%, #21d4fd 100%);">
                                    <div class="text-center">
                                        <!-- Badge at top -->
                                        <div class="d-flex align-items-center justify-content-center mb-2">
                                            <span class="badge bg-danger me-2">Trending</span>
                                        </div>

                                        <!-- Store info with horizontal layout -->
                                        <div class="d-flex align-items-center justify-content-center mb-3">
                                            <div class="rounded-circle bg-white ms-1 me-4 mb-2 border d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
                                                <img src="https://ik.imagekit.io/v4piwnuuh/cashback_goat/media/home/<USER>/logo/durangoboots-mark-fullcolor-14392_4x.png" style="width: 80%; height: 80%; object-fit: contain;" alt="Durango Boots Father's Day Sale">
                                            </div>
                                            <div class="text-start">
                                                <h4 class="text-white mb-0">Boots for Dad</h4>
                                                <p class="text-white text-sm mb-0">Shop Durango's</p>
                                                <p class="text-white text-sm mb-0">Father's Day Sales & Coupons</p>
                                            </div>
                                        </div>

                                        <!-- Button with same style as main feature -->
                                        <div class="buttons">
                                            <a href="{% url 'store' store_slug='durango-boots' %}" class="btn btn-sm btn-rounded bg-white mt-2">
                                                Shop Now
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 3rd Card - eBay week -->
                        <div class="carousel-item">
                            <div class="card shadow-lg overflow-hidden" style="height: 220px;">
                                <div class="card-body d-flex flex-column justify-content-center p-4 bg-gradient-primary h-100" style="background: linear-gradient(145deg, #1a1f37 0%, #212529 100%);">
                                    <div class="text-center">
                                        <!-- Badge at top -->
                                        <div class="d-flex align-items-center justify-content-center mb-2">
                                            <span class="badge bg-danger me-2">Ending Soon</span>
                                        </div>

                                        <!-- Store info with horizontal layout -->
                                        <div class="d-flex align-items-center justify-content-center mb-3">
                                            <div class="rounded-circle bg-white ms-1 me-4 mb-2 border d-flex align-items-center justify-content-center" style="width: 60px; height: 60px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
                                                <img src="https://ik.imagekit.io/v4piwnuuh/cashback_goat/media/home/<USER>/logo/ebay-mark-fullcolor-3993_4x.png" style="width: 80%; height: 80%; object-fit: contain;" alt="ebay sales">
                                            </div>
                                            <div class="text-start">
                                                <h4 class="text-white mb-0">Ebay Week</h4>
                                                <p class="text-white text-sm mb-0">Up to 40% Extra Cashback</p>
                                            </div>
                                        </div>

                                        <!-- Button with same style as main feature -->
                                        <div class="buttons">
                                            <a href="{% url 'store' store_slug='ebay' %}"  class="btn btn-sm btn-rounded bg-white mt-2">
                                                Shop Now
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            <!-- Quick Categories Bar -->
            {# Removed redundant categories bar since it's already in header navigation #}

        </div>
    </section>
    <!-- -------- END Featured Section ------- -->

    <!-- -------- START Store Discovery Section ------- -->
    <section class="pt-2 pt-md-4 pb-2 pb-md-4">
        <div class="container px-2">
            <!-- Ultra-compact header with integrated tabs and subtle view all link -->
            <div class="d-flex align-items-center justify-content-between mb-2">
                <div class="d-flex align-items-center">
                    <h6 class="mb-0 fw-bold">Store Discovery</h6>
                    <a href="{% url 'store-category' %}" class="ms-2 text-xs text-primary">
                        <small>View all</small>
                    </a>
                </div>

                <!-- Ultra-minimalist tab navigation matching section title style -->
                <div class="nav-wrapper">
                    <ul class="nav nav-micro d-inline-flex align-items-center">
                        <li class="nav-item mx-2">
                            <a class="nav-link p-0 active" data-bs-toggle="tab" href="#trending" role="tab" aria-label="Trending" id="trending-tab">
                                <i class="fas fa-fire fa-xs text-primary"></i>
                            </a>
                        </li>
                        <li class="nav-item mx-2">
                            <a class="nav-link p-0" data-bs-toggle="tab" href="#new" role="tab" aria-label="New" id="new-tab">
                                <i class="fas fa-store fa-xs text-secondary"></i>
                            </a>
                        </li>
                        {% if user.is_authenticated %}
                            <li class="nav-item mx-2">
                                <a class="nav-link p-0" data-bs-toggle="tab" href="#favorites" role="tab" aria-label="Favorites" id="favorites-tab">
                                    <i class="fas fa-heart fa-xs text-secondary"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </div>
            </div>

            <!-- Store Content with swipeable row for mobile -->
            <div class="tab-content">

                <!-- Trending Tab -->
                <div class="tab-pane fade show active" id="trending" role="tabpanel">
                    <!-- Mobile Swipeable Row (visible on small screens) -->
                    <div class="d-md-none">
                        <div class="store-scroll-container" style="overflow-x: auto; white-space: nowrap; scroll-snap-type: x mandatory; -webkit-overflow-scrolling: touch; padding-bottom: 5px;">
                            {% for store in trending_stores %}
                                <div class="store-item">
                                    <a href="{% url 'store' store.slug %}" class="text-decoration-none">
                                        <div class="rounded-circle bg-white mx-auto mb-1 border d-flex align-items-center justify-content-center" style="width: 70px; height: 70px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
                                            <img src="{{ store.icon_image }}" alt="Best {{ store.name }} Cashback, Deals & Coupons" style="width: 70%; height: 70%; object-fit: contain;">
                                        </div>
                                        <p class="mb-0 text-xs text-center text-truncate">{{ store.name }}</p>
                                        <div class="text-center">
                                            <span class="badge bg-primary text-xxs">{{ store.cashback_string }}</span>
                                        </div>
                                    </a>
                                </div>
                            {% empty %}
                                <div class="text-center py-2 w-100">
                                    <i class="fas fa-fire mb-1" style="font-size: 24px; opacity: 0.5;"></i>
                                    <p class="small mb-1">Loading trending stores</p>
                                </div>
                            {% endfor %}
                        </div>

                        <!-- Subtle scroll indicator -->
                        <div class="text-center mt-1">
                            <div class="d-inline-flex gap-1">
                                <div style="width: 16px; height: 2px; background-color: #5e72e4;"></div>
                                <div style="width: 8px; height: 2px; background-color: #dee2e6;"></div>
                                <div style="width: 8px; height: 2px; background-color: #dee2e6;"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Desktop Carousel (visible on medium screens and up) -->
                    <div class="d-none d-md-block">
                        <div class="position-relative">
                            <div class="row g-3 store-scroll-row">
                                {% for store in trending_stores %}
                                    <div class="col-lg-8-stores col-md-2 col-sm-4">
                                        <a href="{% url 'store' store.slug %}" class="text-decoration-none">
                                            <div class="text-center">
                                                <div class="rounded-circle bg-white mx-auto mb-2 border d-flex align-items-center justify-content-center move-on-hover" style="width: 90px; height: 90px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
                                                    <img src="{{ store.icon_image }}" alt="{{ store.name }} Cashback, Deals & Coupons" style="width: 85%; height: 85%; object-fit: contain;" loading="lazy">
                                                </div>
                                                <p class="mb-0 text-sm text-truncate">{{ store.name }}</p>
                                                <div class="text-center mt-1">
                                                    <span class="badge bg-primary text-xxs">{{ store.cashback_string }}</span>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                {% empty %}
                                    <div class="col-12 text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-fire mb-2" style="font-size: 36px; opacity: 0.5;"></i>
                                            <h6>Loading Trending Stores...</h6>
                                            <p class="small">Check back soon for the latest trending stores!</p>
                                        </div>
                                    </div>
                                {% endfor %}

                            <!-- Navigation arrows -->
                                {% if trending_stores|length > 6 %}
                                    <button class="btn btn-sm btn-white btn-icon-only shadow-sm position-absolute start-0 top-50 translate-middle-y ms-n3 rounded-circle z-index-3 opacity-9"
                                            style="width: 36px; height: 36px;"
                                            onclick="scrollStores('trending', 'left')">
                                        <i class="fas fa-chevron-left"></i>
                                    </button>
                                    <button class="btn btn-sm btn-white btn-icon-only shadow-sm position-absolute end-0 top-50 translate-middle-y me-n3 rounded-circle z-index-3 opacity-9"
                                            style="width: 36px; height: 36px;"
                                            onclick="scrollStores('trending', 'right')">
                                        <i class="fas fa-chevron-right"></i>
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>


                <!-- New Stores Tab -->
                <div class="tab-pane fade" id="new" role="tabpanel">
                    <!-- Mobile Swipeable Row (visible on small screens) -->
                    <div class="d-md-none">
                        <div class="store-scroll-container" style="overflow-x: auto; white-space: nowrap; scroll-snap-type: x mandatory; -webkit-overflow-scrolling: touch; padding-bottom: 5px;">
                            {% for store in new_stores %}
                                <div class="store-item">
                                    <a href="{% url 'store' store.slug %}" class="text-decoration-none">
                                        <div class="rounded-circle bg-white mx-auto mb-1 border d-flex align-items-center justify-content-center" style="width: 70px; height: 70px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
                                            <img src="{{ store.icon_image }}" alt="{{ store.name }} Cashback, Deals & Coupons" style="width: 70%; height: 70%; object-fit: contain;">
                                        </div>
                                        <p class="mb-0 text-xs text-center text-truncate">{{ store.name }}</p>
                                        <div class="text-center">
                                            <span class="badge bg-primary text-xxs">{{ store.cashback_string }}</span>
                                        </div>
                                    </a>
                                </div>
                            {% empty %}
                                <div class="text-center py-2 w-100">
                                    <i class="fas fa-store mb-1" style="font-size: 24px; opacity: 0.5;"></i>
                                    <p class="small mb-1">New stores coming soon</p>
                                </div>
                            {% endfor %}
                        </div>

                        <!-- Subtle scroll indicator -->
                        <div class="text-center mt-1">
                            <div class="d-inline-flex gap-1">
                                <div style="width: 16px; height: 2px; background-color: #5e72e4;"></div>
                                <div style="width: 8px; height: 2px; background-color: #dee2e6;"></div>
                                <div style="width: 8px; height: 2px; background-color: #dee2e6;"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Desktop Carousel (visible on medium screens and up) -->
                    <div class="d-none d-md-block">
                        <div class="position-relative">
                            <div class="row g-3 store-scroll-row">
                                {% for store in new_stores %}
                                    <div class="col-lg-8-stores col-md-3 col-sm-4">
                                        <a href="{% url 'store' store.slug %}" class="text-decoration-none">
                                            <div class="text-center">
                                                <div class="rounded-circle bg-white mx-auto mb-2 border d-flex align-items-center justify-content-center move-on-hover" style="width: 90px; height: 90px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
                                                    <img src="{{ store.icon_image }}" alt="{{ store.name }} Cashback, Deals & Coupons" style="width: 85%; height: 85%; object-fit: contain;" loading="lazy">
                                                </div>
                                                <p class="mb-0 text-sm text-truncate">{{ store.name }}</p>
                                                <div class="text-center mt-1">
                                                    <span class="badge bg-primary text-xxs">{{ store.cashback_string }}</span>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                {% empty %}
                                    <div class="col-12 text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-store mb-2" style="font-size: 36px; opacity: 0.5;"></i>
                                            <h6>New Stores Coming Soon</h6>
                                            <p class="small">We're adding new stores every week!</p>
                                        </div>
                                    </div>
                                {% endfor %}

                            <!-- Navigation arrows -->
                                {% if new_stores|length > 6 %}
                                    <button class="btn btn-sm btn-white btn-icon-only shadow-sm position-absolute start-0 top-50 translate-middle-y ms-n3 rounded-circle z-index-3 opacity-9"
                                            style="width: 36px; height: 36px;"
                                            onclick="scrollStores('new', 'left')">
                                        <i class="fas fa-chevron-left"></i>
                                    </button>
                                    <button class="btn btn-sm btn-white btn-icon-only shadow-sm position-absolute end-0 top-50 translate-middle-y me-n3 rounded-circle z-index-3 opacity-9"
                                            style="width: 36px; height: 36px;"
                                            onclick="scrollStores('new', 'right')">
                                        <i class="fas fa-chevron-right"></i>
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Favorites Tab -->
                <div class="tab-pane fade" id="favorites" role="tabpanel">
                    {% if user.is_authenticated %}
                        <!-- Mobile Swipeable Row (visible on small screens) -->
                        <div class="d-md-none">
                            <div class="store-scroll-container" style="overflow-x: auto; white-space: nowrap; scroll-snap-type: x mandatory; -webkit-overflow-scrolling: touch; padding-bottom: 5px;">
                                {% for store in favorite_stores|slice:":12" %}
                                    <div class="store-item">
                                        <a href="{% url 'store' store.slug %}" class="text-decoration-none">
                                            <div class="rounded-circle bg-white mx-auto mb-1 border d-flex align-items-center justify-content-center" style="width: 70px; height: 70px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
                                                <img src="{{ store.icon_image }}" alt="{{ store.name }} Cashback, Deals & Coupons" style="width: 70%; height: 70%; object-fit: contain;">
                                            </div>
                                            <p class="mb-0 text-xs fw-bold text-center text-truncate">{{ store.name }}</p>
                                            <div class="text-center">
                                                <span class="badge bg-primary text-xxs">{{ store.cashback_string }}</span>
                                            </div>
                                        </a>
                                    </div>
                                {% empty %}
                                    <div class="text-center py-2 w-100">
                                        <i class="fas fa-heart mb-1" style="font-size: 24px; opacity: 0.5;"></i>
                                        <p class="small mb-1">No favorites yet</p>
                                        <a href="{% url 'store-category' %}" class="btn btn-xs btn-primary">Explore</a>
                                    </div>
                                {% endfor %}
                            </div>

                            <!-- Subtle scroll indicator -->
                            <div class="text-center mt-1">
                                <div class="d-inline-flex gap-1">
                                    <div style="width: 16px; height: 2px; background-color: #5e72e4;"></div>
                                    <div style="width: 8px; height: 2px; background-color: #dee2e6;"></div>
                                    <div style="width: 8px; height: 2px; background-color: #dee2e6;"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Desktop Carousel (visible on medium screens and up) -->
                        <div class="d-none d-md-block">
                            <div class="position-relative">
                                <div class="row g-3 store-scroll-row">
                                    {% for store in favorite_stores|slice:":12" %}
                                        <div class="col-lg-8-stores col-md-3 col-sm-4">
                                            <a href="{% url 'store' store.slug %}" class="text-decoration-none">
                                                <div class="text-center">
                                                    <div class="rounded-circle bg-white mx-auto mb-2 border d-flex align-items-center justify-content-center move-on-hover" style="width: 90px; height: 90px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
                                                        <img src="{{ store.icon_image }}" alt="{{ store.name }} Cashback, Deals & Coupons" style="width: 85%; height: 85%; object-fit: contain;" loading="lazy">
                                                    </div>
                                                    <p class="mb-0 text-sm text-truncate">{{ store.name }}</p>
                                                    <div class="text-center mt-1">
                                                        <span class="badge bg-primary text-xxs">{{ store.cashback_string }}</span>
                                                    </div>
                                                </div>
                                            </a>
                                        </div>
                                    {% empty %}
                                        <div class="col-12 text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-heart mb-2" style="font-size: 36px; opacity: 0.5;"></i>
                                                <h6>No Favorite Stores Yet</h6>
                                                <p class="small mb-2">Start exploring and save your favorite stores!</p>
                                                <a href="{% url 'store-category' %}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-compass me-1"></i>
                                                    Explore Stores
                                                </a>
                                            </div>
                                        </div>
                                    {% endfor %}

                                    <!-- Navigation arrows -->
                                    {% if favorite_stores|length > 6 %}
                                        <button class="btn btn-sm btn-white btn-icon-only shadow-sm position-absolute start-0 top-50 translate-middle-y ms-n3 rounded-circle z-index-3 opacity-9"
                                                style="width: 36px; height: 36px;"
                                                onclick="scrollStores('favorites', 'left')">
                                            <i class="fas fa-chevron-left"></i>
                                        </button>
                                        <button class="btn btn-sm btn-white btn-icon-only shadow-sm position-absolute end-0 top-50 translate-middle-y me-n3 rounded-circle z-index-3 opacity-9"
                                                style="width: 36px; height: 36px;"
                                                onclick="scrollStores('favorites', 'right')">
                                            <i class="fas fa-chevron-right"></i>
                                        </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </section>
    <!-- -------- END Store Discovery Section ------- -->

    <!-- -------- START Frontpage Deals ------- -->
    <section class="pt-2 pt-md-4 pb-2 pb-md-4">
        <div class="container px-0 px-md-2">
            <div class="d-flex align-items-center justify-content-between mb-2">
                <div class="d-flex align-items-center">
                    <h6 class="mb-0 fw-bold">Featured Deals</h6>
                    <a href="{% url 'posts-all' %}" class="ms-2 text-xs text-primary">
                        <small>View all</small>
                    </a>
                </div>

                <!-- Optional: Ultra-minimalist filter icons matching the Store Discovery section -->
                <div class="nav-wrapper d-none d-md-block">
                    <ul class="nav nav-micro d-inline-flex align-items-center">
                        <li class="nav-item mx-2">
                            <a href="{% url 'posts-all' %}" class="nav-link p-0" aria-label="All Deals">
                                <i class="fas fa-tag fa-xs text-secondary"></i>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            {% include 'common/div-posts-all.html' %}
        </div>
    </section>
    <!-- -------- END Frontpage Deals ------- -->

    <!-- -------- START Stats Section ------- -->
    <section class="pt-2 pt-md-4 pb-2 pb-md-4">
        <div class="container px-2">
            <!-- Consistent header with other sections -->
            <div class="d-flex align-items-center justify-content-between mb-2">
                <div class="d-flex align-items-center">
                    <h6 class="mb-0 fw-bold">Community Impact</h6>
                </div>
            </div>

            <!-- Desktop stats (hidden on mobile) -->
            <div class="row g-2 d-none d-md-flex">
                <div class="col-md-3">
                    <div class="card border-0 shadow-xs h-100">
                        <div class="card-body p-3 text-center">
                            <h3 class="text-gradient text-primary mb-1">$<span id="state1" countTo="36178">40k+</span></h3>
                            <p class="text-xs mb-0">Total Cashback Earned</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-xs h-100">
                        <div class="card-body p-3 text-center">
                            <h3 class="text-gradient text-primary mb-1"><span id="state2" countTo="1200">2k+</span></h3>
                            <p class="text-xs mb-0">Active Members</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-xs h-100">
                        <div class="card-body p-3 text-center">
                            <h3 class="text-gradient text-primary mb-1"><span id="state3" countTo="3500">3.5k+</span></h3>
                            <p class="text-xs mb-0">Partner Stores</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-xs h-100">
                        <div class="card-body p-3 text-center">
                            <h3 class="text-gradient text-primary mb-1"><span id="state4" countTo="40">40</span>%</h3>
                            <p class="text-xs mb-0">Max Cashback Rate</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mobile stats (horizontal scroll) -->
            <div class="d-md-none">
                <div class="stats-scroll-container" style="overflow-x: auto; white-space: nowrap; scroll-snap-type: x mandatory; -webkit-overflow-scrolling: touch; padding-bottom: 5px;">
                    <div class="stat-item">
                        <div class="card border-0 shadow-xs p-3 text-center">
                            <h3 class="text-gradient text-primary mb-1 h5">$<span id="state1-mobile" countTo="36178">40k+</span></h3>
                            <p class="text-xs mb-0">Total Cashback</p>
                        </div>
                    </div>

                    <div class="stat-item">
                        <div class="card border-0 shadow-xs p-3 text-center">
                            <h3 class="text-gradient text-primary mb-1 h5"><span id="state2-mobile" countTo="1200">2k+</span></h3>
                            <p class="text-xs mb-0">Active Members</p>
                        </div>
                    </div>

                    <div class="stat-item">
                        <div class="card border-0 shadow-xs p-3 text-center">
                            <h3 class="text-gradient text-primary mb-1 h5"><span id="state3-mobile" countTo="3500">3.5k+</span></h3>
                            <p class="text-xs mb-0">Partner Stores</p>
                        </div>
                    </div>

                    <div class="stat-item">
                        <div class="card border-0 shadow-xs p-3 text-center">
                            <h3 class="text-gradient text-primary mb-1 h5"><span id="state4-mobile" countTo="40">40</span>%</h3>
                            <p class="text-xs mb-0">Max Cashback</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- -------- END Stats Section ------- -->

    {% if not user.is_authenticated %}
    <!-- Mobile sticky bottom bar - Matching store page exactly -->
        <div class="d-md-none fixed-bottom bg-white p-2 border-top shadow-lg">
            <div class="container px-2">
                <a href="{% url 'account_signup' %}" class="btn btn-primary btn-lg w-100 shadow-sm py-2 mb-0">
                    <i class="fas fa-gift me-2"></i>Join & Get $10
                </a>
            </div>
        </div>
    {% endif %}

{% endblock content %}

{% block extra_js %}
    <script src="{% static 'assets/js/plugins/choices.min.js' %}"></script>
    <script src="{% static 'assets/js/plugins/flatpickr.min.js' %}"></script>
    <script src="{% static 'assets/js/plugins/countup.min.js' %}"></script>

    <script type="text/javascript">
        if (document.getElementById('state1')) {
            const countUp = new CountUp('state1', document.getElementById("state1").getAttribute("countTo"));
            if (!countUp.error) {
                countUp.start();
            }
        }
        if (document.getElementById('state2')) {
            const countUp1 = new CountUp('state2', document.getElementById("state2").getAttribute("countTo"));
            if (!countUp1.error) {
                countUp1.start();
            }
        }
        if (document.getElementById('state3')) {
            const countUp2 = new CountUp('state3', document.getElementById("state3").getAttribute("countTo"));
            if (!countUp2.error) {
                countUp2.start();
            }
        }
        if (document.getElementById('state4')) {
            const countUp3 = new CountUp('state4', document.getElementById("state4").getAttribute("countTo"));
            if (!countUp3.error) {
                countUp3.start();
            }
        }

        // Mobile counters
        if (document.getElementById('state1-mobile')) {
            const countUp = new CountUp('state1-mobile', document.getElementById("state1-mobile").getAttribute("countTo"));
            if (!countUp.error) {
                countUp.start();
            }
        }
        if (document.getElementById('state2-mobile')) {
            const countUp1 = new CountUp('state2-mobile', document.getElementById("state2-mobile").getAttribute("countTo"));
            if (!countUp1.error) {
                countUp1.start();
            }
        }
        if (document.getElementById('state3-mobile')) {
            const countUp2 = new CountUp('state3-mobile', document.getElementById("state3-mobile").getAttribute("countTo"));
            if (!countUp2.error) {
                countUp2.start();
            }
        }
        if (document.getElementById('state4-mobile')) {
            const countUp3 = new CountUp('state4-mobile', document.getElementById("state4-mobile").getAttribute("countTo"));
            if (!countUp3.error) {
                countUp3.start();
            }
        }
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Store scroll functionality
            const storeRows = document.querySelectorAll('.store-scroll-row');
            storeRows.forEach(row => {
                row.style.display = 'flex';
                row.style.flexWrap = 'nowrap';
                row.style.overflowX = 'hidden';
                row.style.scrollBehavior = 'smooth';
                row.style.scrollSnapType = 'x mandatory';

                const cols = row.querySelectorAll('.col-lg-2');
                cols.forEach(col => {
                    col.style.flex = '0 0 auto';
                    col.style.scrollSnapAlign = 'start';
                });
            });

            // Initialize choices for select elements
            document.querySelectorAll('.choices-select').forEach(select => {
                new Choices(select, {
                    searchEnabled: false
                });
            });

            // Initialize datepickers
            if (document.querySelector('.datepicker')) {
                flatpickr('.datepicker', {
                    mode: "range"
                });
            }

            // Fix active state for store discovery tabs
            const storeTabLinks = document.querySelectorAll('[data-bs-toggle="tab"]');
            storeTabLinks.forEach(link => {
                link.addEventListener('shown.bs.tab', function(e) {
                    // Remove active class and text-primary from all tab icons
                    storeTabLinks.forEach(tab => {
                        const icon = tab.querySelector('i');
                        if (icon) {
                            icon.classList.remove('text-primary');
                            icon.classList.add('text-secondary');
                        }
                    });

                    // Add active class and text-primary to the clicked tab icon
                    const activeIcon = e.target.querySelector('i');
                    if (activeIcon) {
                        activeIcon.classList.remove('text-secondary');
                        activeIcon.classList.add('text-primary');
                    }
                });
            });
        });

        // Function to scroll store rows horizontally
        function scrollStores(tabId, direction) {
            const container = document.querySelector(`#${tabId} .store-scroll-row`);
            if (!container) return;

            const scrollAmount = container.offsetWidth / 2;
            const currentScroll = container.scrollLeft;

            if (direction === 'left') {
                container.scrollTo({
                    left: Math.max(0, currentScroll - scrollAmount),
                    behavior: 'smooth'
                });
            } else {
                container.scrollTo({
                    left: currentScroll + scrollAmount,
                    behavior: 'smooth'
                });
            }
        }
    </script>
{% endblock extra_js %}

<!-- Before footer, ensure proper spacing -->
<div class="py-2 py-md-4"></div>
