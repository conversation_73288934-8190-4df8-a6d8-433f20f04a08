{% extends "layouts/base.html" %}
{% load static %}
{% load custom_filters %}

{% block title %}{% if current_category %}{{ blog_categories|get_item:current_category }} Tips & Guides - Money Saving Blog{% else %}Money Saving Tips & Cashback Guides{% endif %} | CashbackGoat{% endblock %}

{% block meta-description %}{% if current_category %}Expert {{ blog_categories|get_item:current_category|lower }} tips and guides to help you save money and earn cashback. Find the best deals and money-saving strategies for {{ blog_categories|get_item:current_category|lower }}.{% else %}Discover expert money-saving tips, cashback guides, and smart shopping advice. Learn how to maximize your savings and earn cashback on every purchase with CashbackGoat.{% endif %}{% endblock %}

{% block additional-schema %}
    <script type="application/ld+json">
        {
          "@context": "https://schema.org",
          "@type": "Blog",
          "name": "CashbackGoat Blog",
          "description": "Money saving tips, cashback guides, and smart shopping advice to help you save more on every purchase.",
          "url": "{{ request.build_absolute_uri }}",
          "publisher": {
            "@type": "Organization",
            "name": "CashbackGoat",
            "logo": {
              "@type": "ImageObject",
              "url": "{{ request.scheme }}://{{ request.get_host }}{% static 'assets/img/logo.png' %}"
            }
          },
          "blogPost": [
        {% for blog in blogs %}
            {
              "@type": "BlogPosting",
              "headline": "{{ blog.title }}",
              "description": "{{ blog.summary|truncatewords:30 }}",
              "url": "{{ request.scheme }}://{{ request.get_host }}{{ blog.get_absolute_url }}",
              "datePublished": "{{ blog.created_at|date:'c' }}",
              "dateModified": "{{ blog.updated_at|date:'c' }}",
              "author": {
                "@type": "Person",
                "name": "CG Editor"
              }{% if blog.featured_image %},
              "image": {
                "@type": "ImageObject",
                "url": "{{ request.scheme }}://{{ request.get_host }}{{ blog.featured_image.url }}"
              }{% endif %}
            }{% if not forloop.last %},{% endif %}
            {% endfor %}
        ]
      }
    </script>

    <script type="application/ld+json">
        {
          "@context": "https://schema.org",
          "@type": "BreadcrumbList",
          "itemListElement": [
            {
              "@type": "ListItem",
              "position": 1,
              "name": "Home",
              "item": "{{ request.scheme }}://{{ request.get_host }}/"
            },
            {
              "@type": "ListItem",
              "position": 2,
              "name": "Blog",
              "item": "{{ request.scheme }}://{{ request.get_host }}{% url 'blog-list' %}"
            }{% if current_category %},
            {
              "@type": "ListItem",
              "position": 3,
              "name": "{{ blog_categories|get_item:current_category }}",
              "item": "{{ request.scheme }}://{{ request.get_host }}{% url 'blog-list-category' category=current_category %}"
            }{% endif %}
        ]
      }
    </script>
{% endblock %}

{% block content %}


    <div class="container">
        <!-- Breadcrumb - Visible only on medium screens and larger -->
        <nav aria-label="breadcrumb" class="mb-3 d-none d-md-block">
            <ol class="breadcrumb bg-transparent mb-0">
                <li class="breadcrumb-item"><a href="/" class="text-sm">Home</a></li>
                <li class="breadcrumb-item"><a href="{% url 'blog-list' %}" class="text-sm">Blog</a></li>
                {% if current_category %}
                    <li class="breadcrumb-item active text-sm" aria-current="page">{{ blog_categories|get_item:current_category }}</li>
                {% endif %}
            </ol>
        </nav>
        <div class="row">
            <div class="col-lg-7">
                <!-- START Blogs w/ 3 rows w/ image on left & title, text, author on end-->
                <section class="py-3">
                    {% for blog in blogs %}
                        <div class="card card-plain card-blog mb-5">
                            <div class="row">
                                <div class="col-lg-4 col-md-4">
                                    <div class="card-image position-relative border-radius-lg">
                                        <div class="blur-shadow-image">
                                            {% if blog.featured_image %}
                                                <img class="img border-radius-lg" src="{{ blog.featured_image.url }}"
                                                     alt="{{ blog.title }}">
                                            {% else %}
                                                <img class="img border-radius-lg"
                                                     src="{% get_media_prefix %}home/blog/images/default/blog_list_img_1.jpeg"
                                                     alt="architecture">
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-7 col-md-7 my-sm-auto mt-3 ms-sm-3">
                                    <h4>
                                        <a href="{{ blog.get_absolute_url }}" class="text-dark">{{ blog.title }}</a>
                                    </h4>
                                    <p>
                                        {{ blog.summary|truncatewords:30 }} <a href="{{ blog.get_absolute_url }}"
                                                                               class="text-gradient text-primary text-decoration-none">
                                        Read More </a>
                                    </p>
                                    <div class="author">
                                        <img src="{% static 'assets/img/team-4.jpg' %}" alt="blog_author"
                                             class="avatar avatar-sm shadow me-2">
                                        <p class="my-auto">CG Editor</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% empty %}
                        <div class="col-12 text-center py-5">
                            <p class="text-muted">No blog posts found in this category.</p>
                        </div>
                    {% endfor %}

                </section>
                <!-- END Blogs w/ 3 rows w/ image on left & title, text, author on end-->
                <div class="card card-blog card-background">
                    <div class="full-background"
                         style="background-image: url('https://raw.githubusercontent.com/creativetimofficial/public-assets/master/soft-ui-design-system/assets/img/desert.jpg')"></div>
                    <div class="card-body body-left">
                        <div class="content-left text-start">
                            <h2 class="card-title text-white">Money Saving Tips & Guides.</h2>
                            <p class="card-description">Expert advice on cashback, smart shopping, and saving money</p>
                            <div class="author">
                                <img src="{% static 'assets/img/team-2.jpg' %}" alt="..." class="avatar mr-2">
                                <div class="name ms-2">
                                    <span>Mathew G</span>
                                    <div class="stats">
                                        <small>CG Editor</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- START paginator -->
                {% if blogs.paginator.num_pages >= 1 %}
                    <div class="d-flex justify-content-start mt-4">
                        <nav aria-label="Page navigation">
                            <ul class="pagination pagination-primary m-0">
                                {% if blogs.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=
                                                {{ blogs.previous_page_number }}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}"
                                           aria-label="Previous">
                                            <span aria-hidden="true"><i class="fa fa-angle-double-left"
                                                                        aria-hidden="true"></i></span>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link"><i class="fa fa-angle-double-left"
                                                                   aria-hidden="true"></i></span>
                                    </li>
                                {% endif %}

                                {% for num in blogs.paginator.page_range %}
                                    {% if blogs.number == num %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ num }}</span>
                                        </li>
                                    {% elif num > blogs.number|add:'-3' and num < blogs.number|add:'3' %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=
                                                    {{ num }}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}

                                {% if blogs.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=
                                                {{ blogs.next_page_number }}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}"
                                           aria-label="Next">
                                            <span aria-hidden="true"><i class="fa fa-angle-double-right"
                                                                        aria-hidden="true"></i></span>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link"><i class="fa fa-angle-double-right"
                                                                   aria-hidden="true"></i></span>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                {% endif %}
                <!-- END paginator -->
            </div>
            <div class="col-lg-4 ml-auto">
                <div class="pt-1 pb-5 position-sticky top-1 mt-lg-4 mt-5">
                    <h4>Newsletter</h4>
                    <p>Get access to subscriber exclusive deals and be the first who gets informed about cashback
                        news.</p>
                    <div class="my-3">
                        <input type="text" class="form-control mb-sm-0 mb-2" placeholder="Email Here...">
                    </div>
                    <button type="button" class="btn bg-gradient-primary">Subscribe</button>
                    <h4 class="mt-5">Trending Deals</h4>
                    {% for post in trending_posts %}
                        <a href="{{ post.get_absolute_url }}" class="text-decoration-none">
                            <div class="card hover-shadow-sm transition-all duration-200 mb-2">
                                <div class="card-body py-2 px-3 d-flex align-items-center">
                                    <div class="post-image me-2" style="min-width: 65px;">
                                        {% if post.postimage_set.all and post.postimage_set.all.0.post_image %}
                                            <div class="rounded-2 shadow-sm overflow-hidden"
                                                 style="width: 65px; height: 65px;">
                                                <img src="
                                                        {{ post.postimage_set.all.0.post_image }}"
                                                     class="img-fluid w-100 h-100"
                                                     style="object-fit: cover;"
                                                     alt="{{ post.title }}">
                                            </div>
                                        {% else %}
                                            <div class="bg-gradient-light rounded-2 d-flex align-items-center justify-content-center"
                                                 style="width: 65px; height: 65px;">
                                                <i class="fas fa-tag opacity-8"></i>
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="flex-grow-1">
                                        <p class="text-sm text-dark mb-1 text-truncate-2">
                                            {{ post.title }}
                                        </p>
                                        <div class="d-flex align-items-center text-xxs text-muted">
                                            <i class="fas fa-clock opacity-6 me-1"></i>
                                            {{ post.get_post_time_diff }}
                                        </div>
                                    </div>
                                    <div class="ms-auto ps-2">
                                        <i class="fas fa-angle-right text-muted opacity-6"></i>
                                    </div>
                                </div>
                            </div>
                        </a>
                    {% empty %}
                        <div class="card mb-2">
                            <div class="card-body py-2 px-3 text-center">
                                <p class="text-muted text-sm mb-0">No trending deals at the moment</p>
                            </div>
                        </div>
                    {% endfor %}
                    <h4 class="mt-5">Top Stores</h4>
                    <div class="row g-3">
                        <div class="col-4">
                            <a href="{% url 'store' store_slug='ebay' %}" class="text-decoration-none">
                                <div class="text-center">
                                    <div class="rounded-circle bg-white mx-auto mb-2 border d-flex align-items-center justify-content-center move-on-hover"
                                         style="width: 90px; height: 90px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
                                        <img src="https://res.cloudinary.com/dk44zljui/image/upload/v1744774612/store/logo/3297_ebay-mark-fullcolor-3993%404x.png"
                                             alt="eBay"
                                             style="width: 70%; height: 70%; object-fit: contain;">
                                    </div>
                                    <p class="mb-0 text-sm text-truncate">eBay</p>

                                </div>
                            </a>
                        </div>

                        <div class="col-4">
                            <a href="{% url 'store' store_slug='amazon' %}" class="text-decoration-none">
                                <div class="text-center">
                                    <div class="rounded-circle bg-white mx-auto mb-2 border d-flex align-items-center justify-content-center move-on-hover"
                                         style="width: 90px; height: 90px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
                                        <img src="https://res.cloudinary.com/dk44zljui/image/upload/v1744776482/store/logo/3569_amazon-logo.png"
                                             alt="Amazon"
                                             style="width: 70%; height: 70%; object-fit: contain;">
                                    </div>
                                    <p class="mb-0 text-sm text-truncate">Amazon</p>

                                </div>
                            </a>
                        </div>

                        <div class="col-4">
                            <a href="{% url 'store' store_slug='bookingcom' %}" class="text-decoration-none">
                                <div class="text-center">
                                    <div class="rounded-circle bg-white mx-auto mb-2 border d-flex align-items-center justify-content-center move-on-hover"
                                         style="width: 90px; height: 90px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
                                        <img src="https://res.cloudinary.com/dk44zljui/image/upload/v1744774399/store/logo/6_bookingdotcom-mark-fullcolor-11603%404x.png"
                                             alt="Booking.com"
                                             style="width: 70%; height: 70%; object-fit: contain;">
                                    </div>
                                    <p class="mb-0 text-sm text-truncate">Booking.com</p>

                                </div>
                            </a>
                        </div>
                    </div>


                    <h4 class="mt-5 mb-4">Tags</h4>
                    {% for code, name in blog_categories.items %}
                        <span class="badge bg-light text-dark">
                            <a href="{% url 'blog-list-category' category=code %}"
                               class="{% if current_category == code %}text-primary{% else %}text-dark{% endif %}">
                            {{ name }}
                            </a>
                        </span>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

{% endblock content %}



{% block extra_js %}

    <script src="{% static 'assets/js/plugins/choices.min.js' %}"></script>
    <script src="{% static 'assets/js/plugins/flatpickr.min.js' %}"></script>

    <script>
        if (document.getElementById('language-select')) {
            var element = document.getElementById('language-select');
            const example = new Choices(element, {
                searchEnabled: false
            });

        }
        if (document.getElementById('currency-select')) {
            var element = document.getElementById('currency-select');
            const example = new Choices(element, {
                searchEnabled: false
            });
        }
    </script>

{% endblock extra_js %}