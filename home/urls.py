from django.urls import path

from . import views

urlpatterns = [
    # homepage
    path('', views.index, name='index'),
    # path('test/', views.test, name='test'), # test page
    path('test/', views.test, name='blog-detail'),

    # TODO remove by 5/31/2025
    # deprecated Authentication (replaced by django-allauth)
    # path('accounts/login/', views.UserLoginView.as_view(), name='login'),
    # path('accounts/register/', views.register, name='register'),
    # path('accounts/logout/', views.logout_view, name='logout'),
    # path('accounts/password-change/', views.UserPasswordChangeView.as_view(), name='password_change'),
    # path('accounts/password-change-done/', auth_views.PasswordChangeDoneView.as_view(
    #     template_name='accounts/password_change_done.html'
    # ), name='password_change_done'),
    # path('accounts/password-reset/', views.UserPasswordResetView.as_view(), name='password_reset'),
    # path('accounts/password-reset-done/', auth_views.PasswordResetDoneView.as_view(
    #     template_name='accounts/password_reset_done.html'
    # ), name='password_reset_done'),
    # path('accounts/password-reset-confirm/<uidb64>/<token>/',
    #      views.UserPasswordResetConfirmView.as_view(), name='password_reset_confirm'),
    # path('accounts/password-reset-complete/', auth_views.PasswordResetCompleteView.as_view(
    #     template_name='accounts/password_reset_complete.html'
    # ), name='password_reset_complete'),

    # user account page ()
    path('account/home', views.account_home, name='account-home'),
    path('account/activities/', views.account_activities, name='account-activities'),
    path('account/favorites/', views.account_favorites, name='account-favorites'),
    path('account/privacy/', views.account_privacy, name='account-privacy'),
    path('account/referrals/', views.account_referrals, name='account-referrals'),
    path('account/settings/', views.account_settings, name='account-settings'),

    path('account/update-personal-info/', views.update_personal_info, name='update-personal-info'),
    path('account/add-payment-method/', views.add_payment_method, name='add-payment-method'),
    path('account/set-active-payment-method/', views.set_active_payment_method, name='set-active-payment-method'),
    path('account/delete-payment-method/', views.delete_payment_method, name='delete-payment-method'),
    # Django all-auth has built in views for login, signup, logout etc, with path e.g. ../accounts/login/
    path('account/password-change/', views.password_change, name='password-change'),  # for logged-in users

    # articles
    path('articles/all/', views.articles_all, name='articles-all'),
    path('articles/<str:article_name>/', views.article_details, name='article-details'),

    # Blog URLs
    path('blog/', views.blog_list, name='blog-list'),
    path('blog/category/<str:category>/', views.blog_list, name='blog-list-category'),
    path('blog/<slug:slug>/', views.blog_detail, name='blog-detail'),

    # categories and stores
    path("categories/all/", views.store_category, name="store-category"),
    path("categories/<str:category_slug>/", views.store_category, name="store-category"),

    path("stores/<str:store_slug>/", views.store, name="store"),
    path("search", views.search, name="search"),

    path('help/', views.help, name='help'),
    path('personal-data-management/', views.personal_data_management, name='personal-data-management'),

    # deal posts
    path('posts/all/', views.posts_all, name='posts-all'),
    path('posts/<str:post_slug>/', views.post_info, name='post-info'),

    # click redirection links
    path('click/tl=<str:tracked_link>/', views.click_link, name='click-link'),

    # misc pages
    path('about-us/', views.about_us, name='about-us'),
    path('contact-us/', views.contact_us, name='contact-us'),
    path('error-404', views.error_404, name='error-404'),
    path('error-500', views.error_500, name='error-500'),
    path('faq/', views.faq, name='faq'),
    path('privacy/', views.privacy, name='privacy'),
    path('referral/', views.referral, name='referral'),
    path('success/', views.success, name='success'),  # success page for contact-us form submission
    path('terms/', views.terms, name='terms'),

    # APIs
    path('api/store-search-autocomplete/', views.store_search_autocomplete, name='store-search-autocomplete'),
    path('api/test-email/', views.test_email, name='test-email'),  # send a test email to admin/superuser
    path('api/toggle-favorite-store/', views.toggle_favorite_store, name='toggle-favorite-store'),
]
