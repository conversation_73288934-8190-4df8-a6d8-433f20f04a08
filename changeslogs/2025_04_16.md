# Changes Summary

add fallback images for Stores and PostImage model
- use media urls if any (cloudinary url)
- fallback to files hosted on web/local server

## backup data

bash scripts/remote_pg_backup.sh
bash scripts/prod_db_to_dev_db_sync.sh

```sql
create table public.home_store_bk_20250416 as
select * from public.home_store;

create table public.home_postimage_bk_20250416 as
select * from public.home_postimage;
```

## code upload
rsync -avh ~/Code/cashback_goat rw@do-droplet:~/code/ --delete \
--exclude={'.env.local','.git/','.idea/','db_sync','home/migrations/','static/','static_collected/','venv/','*/__pycache__/'}

rsync -avh ~/Code/cashback_goat/static/* rw@do-droplet:~/code/cashback_goat/static_collected/ --exclude venv/ \
--exclude .git/ --delete

## Migration plan

```shell
cd ~/code/cashback_goat
source venv/bin/activate

python manage.py makemigrations
python manage.py migrate
```

for local and prod:
```sql
    INSERT INTO public.home_postimage(post_id,
                                      scraped_item_id,
                                      scraped_source,
                                      img_url,
                                      img_file,
                                      created_at,
                                      updated_at)
    values (%s, COALESCE(%s, ''), %s, %s, %s, %s, %s)
    ON CONFLICT(post_id, scraped_item_id) DO UPDATE
    SET img_url = EXCLUDED.img_url, img_file = EXCLUDED.img_file;


update public.home_store
set icon_file = 'amazon-logo.png'
where name = 'Amazon';
```
## post deployment

sudo systemctl restart gunicorn

(optional)
sudo systemctl daemon-reload
sudo systemctl restart gunicorn.socket gunicorn.service
sudo systemctl restart nginx

## verify logs

journalctl -u gunicorn -f 
