
update_deals_store_name_query = f"""
UPDATE scraped.deals as sd
    SET override_store = sno.override_store_name
    from scraped.store_name_override sno
    WHERE lower(sd.store) = lower(sno.store_name)
"""



insert_new_scraped_deals_query = f"""
    INSERT INTO scraped.deals(
        id, 
        post_id,
        source,
        store,
        override_store, 
        title,
        description, 
        sales_price, 
        original_price,
        status,
        link_url, 
        img_url, 
        downloaded_img_path,
        created_at,
        updated_at,
        slug
         )
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s) 
    ON CONFLICT(id) DO NOTHING
    RETURNING id;
"""

insert_new_scraped_dealitems_query = f"""
    INSERT INTO scraped.deal_items(
        id,
        deal_id,
        post_id,
        source,
        title,
        description,
        sales_price,
        original_price,
        item_url,
        item_img_url,
        item_downloaded_img_path,
        created_at,
        updated_at
         )
    VALUES ( %s,  %s,  %s,  %s,  %s,  %s,  %s,  %s,  %s,  %s,  %s,  %s,  %s) 
    ON CONFLICT(id) DO NOTHING
    RETURNING id;
"""

select_deals_with_redirect_url_query = """
    select 
        id as deal_id,
        link_url as scraped_url
    from scraped.deals
    where link_url is not null and link_final_url is null
    ORDER BY created_at DESC
    LIMIT 100
    
"""

select_deal_items_with_redirect_url_query = """
    select
        deal_id,
        item_url  as scraped_url
    from scraped.deal_items
    where item_url is not null and item_final_url is null
    ORDER BY created_at DESC
    LIMIT 50
                """

update_deals_redirect_url_query = """ 
    UPDATE scraped.deals
    SET link_final_url = %s, updated_at = %s
    WHERE id = %s and link_url = %s
"""

update_deal_items_redirect_url_query = """ 
                        UPDATE scraped.deal_items
                        SET item_final_url = %s, updated_at = %s
                        WHERE deal_id = %s and item_url = %s
                        """