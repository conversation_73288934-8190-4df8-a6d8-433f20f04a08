import asyncio
import aiohttp
import logging


logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


async def async_aiohttp_get_all_redirection_urls(rows, cookies):
    async with aiohttp.ClientSession(cookies=cookies) as session:
        async def fetch(row):
            async with session.get(row[1]) as response:
                logger.info(f'{row[1]} --> {response.url}' )
                # # yield control for 1 second
                # await asyncio.sleep(1)
                #
                # # wait for the http request to return
                # text = await response.text()
                return row[0], row[1], str(response.url)
        return await asyncio.gather(*[fetch(row) for row in rows])
