import csv
import os
from collections import namedtuple

# header for ../data/all_network_advertiser_mapping.csv
CSV_HEADER = ['store_name', 'advertiser_name', 'network', 'status']
Mapping = namedtuple('Mapping', CSV_HEADER)     # Define a named tuple for better readability


class AdvertiserToStore:
    # csv file of advertiser_name (of all networks) to store_name mappings
    def __init__(self, csv_path='../data/all_network_advertiser_mapping.csv', delimiter='|'):
        self.csv_path = csv_path
        self.delimiter = delimiter
        self.ensure_file_exists()

        self.mappings = self.load_mappings()
        self.advertiser_to_store_mapping = self.get_advertiser_to_store_mapping()  # Structure: {advertiser_name: store_name}
        self.store_to_network_mapping = self.get_store_to_network_mapping()  # Structure: {store_name: network_name}

    def ensure_file_exists(self):
        """Create the CSV file if it doesn't exist"""
        print(f"Ensuring csv file exists at: {self.csv_path}")
        if not os.path.exists(os.path.dirname(self.csv_path)):
            os.makedirs(os.path.dirname(self.csv_path))

        if not os.path.exists(self.csv_path):
            with open(self.csv_path, 'w', newline='') as file:
                writer = csv.writer(file, delimiter=self.delimiter)
                writer.writerow(CSV_HEADER)

    def load_mappings(self):
        """Load the mappings from CSV into memory"""
        try:
            mappings = []
            store_networks = {}  # Temporary dict to check for conflicts

            with open(self.csv_path, 'r', newline='') as file:
                reader = csv.DictReader(file, delimiter=self.delimiter)
                for row in reader:
                    if not row['store_name'] or not row['status'] or row['status'] != 'active':
                        print(f"Skipping row: {row}")
                        continue

                    advertiser_name = row['advertiser_name']
                    store_name = row['store_name']
                    network = row.get('network', 'unknown')  # Default to 'unknown' for backward compatibility
                    status = row['status']

                    # Check for network conflict
                    if store_name in store_networks:
                        existing_network = store_networks[store_name]
                        if existing_network != network:
                            raise ValueError(
                                f"Store '{store_name}' is already active on network '{existing_network}' "
                                f"but trying to activate on '{network}'"
                            )
                    else:
                        store_networks[store_name] = network

                    # Add the mapping
                    mappings.append(Mapping(store_name, advertiser_name, network, status))

            print(f"Loaded {len(mappings)} valid mappings from total of {reader.line_num - 1} CSV rows")
            return mappings
        except Exception as e:
            print(f"Error loading advertiser mapping: {e}")
            raise e


    def get_store_name_for_advertiser(self, advertiser_name):
        """Get the store name for an active advertiser name"""
        return self.advertiser_to_store_mapping.get(advertiser_name)

    def get_network_for_store(self, store_name):
        """Get the network for a store name"""
        return self.store_to_network_mapping.get(store_name)

    def get_networks_for_advertiser(self, advertiser_name):
        """Get all networks available for an advertiser"""
        if advertiser_name in self.advertiser_to_store_mapping:
            return list(self.advertiser_to_store_mapping[advertiser_name].keys())
        return []

    def get_advertiser_to_store_mapping(self):
        """Get mapping as a dictionary: {advertiser_name: store_name}"""
        advertiser_to_store = {}
        for mapping in self.mappings:
            advertiser_to_store[mapping.advertiser_name] = mapping.store_name
        return advertiser_to_store

    def get_store_to_network_mapping(self):
        """Get mapping as a dictionary: {store_name: network_name}"""
        store_to_network = {}
        for mapping in self.mappings:
            store_to_network[mapping.store_name] = mapping.network
        return store_to_network


def main():
    # Example usage
    mapping = AdvertiserToStore()

    # # Add some example mappings for different networks
    # mapping.add_or_update_mapping('Amazon', 'Amazon', 'cj')
    # mapping.add_or_update_mapping('Amazon', 'Amazon', 'flexoffers')
    # mapping.add_or_update_mapping('Best Buy', 'Best Buy', 'cj')
    # mapping.add_or_update_mapping('Target', 'Target', 'impact')

    # Get all entries
    advertiser_to_store_mapping = mapping.get_advertiser_to_store_mapping()
    print(f">> Got {len(advertiser_to_store_mapping)} advertisers mapped to stores..")
    store_to_network_mapping = mapping.get_store_to_network_mapping()
    print(f">> Got {len(store_to_network_mapping)} stores activated on only one network ..")

    for advertiser, store in advertiser_to_store_mapping.items():
        print(f"advertiser={advertiser} -> store={store} -> network={store_to_network_mapping[store]}")


if __name__ == "__main__":
    main()
