"""api utils


"""

import logging
import os
import time
import xml.etree.ElementTree as ET
from xml.sax.saxutils import unescape

import requests

from affiliate.cj.xml_helper import get_element_text

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def fetch_objects_from_api(
        endpoint_url='https://advertiser-lookup.api.cj.com/v2/advertiser-lookup?requestor-cid=7144689&advertiser-ids=joined"',
        object_root_key='advertisers',
        object_child_key='advertiser',
        oject_id_key='advertiser-id',
        pageSize=5, params=''):
    """Fetch objects from CJ API with improved rate limiting handling"""
    # max pageSize varies by endpoint, check api docs
    logger.info(f"Fetching objects from {endpoint_url} with params {params}")
    headers = {
        "Authorization": f"Bearer {os.getenv('CJ_ACCESS_TOKEN')}",
        "Content-Type": "application/xml"
    }

    objects_id_and_response = []  # list of tuples (object_id, response_data_xml)
    page = 1
    total_pages = 1  # Will be updated from the first response
    per_page = 100  # API default, may need to be adjusted

    while page <= total_pages:
        paginated_url = f"{endpoint_url}&page-number={page}"
        response = requests.get(paginated_url, headers=headers)

        if response.status_code != 200:
            logger.info(f"Failed to fetch from {paginated_url}: {response.status_code}")
            logger.info("Response content:", response.text[:500])
            return None

        try:
            # Parse the XML response
            root = ET.fromstring(response.content)

            # Extract pagination information
            object_root_data = root.find(f'.//{object_root_key}')
            if object_root_data is None:
                logger.info(f"No objects found for {object_root_key} in response")
                break

            total_matched = int(object_root_data.get('total-matched', 0))
            records_returned = int(object_root_data.get('records-returned', 0))

            # Calculate total pages if this is the first page
            if page == 1:
                # Use actual records per page from API rather than assuming 100
                per_page = records_returned if records_returned > 0 else per_page
                total_pages = (total_matched + per_page - 1) // per_page if total_matched > 0 else 0
                logger.info(
                    f"{object_root_key} API indicates total of {total_matched} results. \nSetting per_page={per_page} and total_pages={total_pages}")

                if total_matched == 0:
                    logger.info(f"No {object_root_key} found")
                    return []

            # Process advertisers in this page
            count_this_page = 0
            page_results = root.findall(f'.//{object_child_key}')
            for result in page_results:
                objects_id_and_response.append((
                    get_element_text(result, oject_id_key),  # object_id
                    # unescape xml special characters
                    ET.tostring(unescape(result), encoding='unicode')  # response_data as xml string
                ))
                count_this_page += 1

            logger.info(f"Processed {object_root_key} at page={page}/{total_pages}, added {count_this_page} objects")
            page += 1

            # Add a small delay between API requests to avoid rate limiting
            if page <= total_pages:
                time.sleep(0.25)

        except ET.ParseError as e:
            logger.info(f"Error parsing XML for {paginated_url}: {e}")
            logger.info("Response content:", response.text[:500])
            # Continue to next page instead of returning None
            page += 1
            continue
        except Exception as e:
            logger.info(f"Error processing {paginated_url}: {e}")
            # Continue to next page instead of returning None
            page += 1
            continue

    logger.info(f"Total objects collected: {len(objects_id_and_response)}")
    return objects_id_and_response
