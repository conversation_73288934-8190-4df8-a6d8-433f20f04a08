from django.urls import path
from theme_soft_pro import views
from django.contrib.auth import views as auth_views


urlpatterns = [
  path('', views.index, name='index'),
  path('pages/coworking/', views.coworking, name='coworking'),
  path('pages/rental/', views.rental, name='rental'),
  path('pages/case-study/', views.case_study, name='case_study'),
  path('pages/podcast/', views.podcast, name='podcast'),
  path('pages/about-us/', views.about_us, name='about_us'),
  path('pages/pricing/', views.pricing, name='pricing'),
  path('pages/career/', views.career, name='career'),
  path('pages/work-with-us/', views.work_with_us, name='work_with_us'),
  path('pages/terms-and-conditions/', views.terms_conditions, name='terms_conditions'),
  path('pages/help-center/', views.help_center, name='help_center'),
  path('pages/help-center-basic/', views.help_center_basic, name='help_center_basic'),
  path('pages/contact-us/', views.contact_us, name='contact_us'),
  path('pages/contact-us-basic/', views.contact_us_basic, name='contact_us_basic'),
  path('pages/faq/', views.faq, name='faq'),
  path('pages/privacy/', views.privacy, name='privacy'),
  path('pages/mobile-app/', views.mobile_app, name='mobile_app'),
  path('pages/desktop-app/', views.desktop_app, name='desktop_app'),

  # Blog
  path('blog/single-article/', views.single_article, name='single_article'),
  path('blog/blog-posts/', views.blog_posts, name='blog_posts'),
  path('blog/categories/', views.categories, name='categories'),
  path('blog/author/', views.author, name='author'),

  path('pages/automotive/', views.automotive, name='automotive'),
  path('pages/digital-agency/', views.digital_agency, name='digital_agency'),
  path('pages/vr/', views.vr, name='vr'),
  path('pages/smart-home/', views.smart_home, name='smart_home'),
  path('pages/chat/', views.chat, name='chat'),
  path('pages/product-page/', views.product_page, name='product_page'),

  # Sections -> Page Sections
  path('sections/page-header/', views.page_header, name='page_headers'),
  path('sections/features/', views.features, name='features'),
  path('sections/pricing/', views.section_pricing, name='section_pricing'),
  path('sections/faq/', views.sections_faq, name='sections_faq'),
  path('sections/blog-posts/', views.section_blog_posts, name='sections_blog_posts'),
  path('sections/testimonials/', views.testimonials, name='testimonials'),
  path('sections/teams/', views.teams, name='teams'),
  path('sections/stats/', views.stats, name='stats'),
  path('sections/call-to-actions/', views.call_to_actions, name='call_to_actions'),
  path('sections/applications/', views.applications, name='applications'),
  path('sections/logo-areas/', views.logo_areas, name='logo_areas'),
  path('sections/footers/', views.footers, name='footers'),
  path('sections/general-cards/', views.general_cards, name='general_cards'),
  path('sections/content-sections/', views.content_sections, name='content_sections'),

  # Sections -> Navigation
  path('sections/navbars/', views.navbars, name='navbars'),
  path('sections/nav-tabs/', views.nav_tabs, name='nav_tabs'),
  path('sections/pagination/', views.pagination, name='pagination'),
  # Sections -> Input Area
  path('sections/newsletter/', views.newsletter, name='newsletter'),
  path('sections/contact-sections/', views.contact_sections, name='contact_sections'),
  path('sections/inputs/', views.inputs, name='inputs'),
  path('sections/forms/', views.forms, name='forms'),
  # Sections -> Attention Catcher
  path('sections/alerts/', views.alerts, name='alerts'),
  path('sections/modals/', views.modals, name='modals'),
  path('sections/notifications/', views.notifications, name='notifications'),
  path('sections/tooltips-popovers/', views.tooltips_popovers, name='tooltips_popovers'),
  # Sections -> Elements
  path('sections/avatars/', views.avatars, name='avatars'),
  path('sections/badges/', views.badges, name='badges'),
  path('sections/breadcrumbs/', views.breadcrumbs, name='breadcrumbs'),
  path('sections/button-groups/', views.button_groups, name='button_groups'),
  path('sections/buttons/', views.buttons, name='buttons'),
  path('sections/dropdowns/', views.dropdowns, name='dropdowns'),
  path('sections/progress-bars/', views.progress_bars, name='progress_bars'),
  path('sections/social-buttons/', views.social_buttons, name='social_buttons'),
  path('sections/tables/', views.tables, name='tables'),
  path('sections/toggles/', views.toggles, name='toggles'),
  path('sections/typography/', views.typography, name='typography'),

  # Authentication -> Login
  path('accounts/login/basic/', views.BasicLoginView.as_view(), name='basic_login'),
  path('accounts/login/cover/', views.CoverLoginView.as_view(), name='cover_login'),
  path('accounts/login/illustration/', views.IllustrationLoginView.as_view(), name='illustration_login'),
  path('accounts/login/simple/', views.SimpleLoginView.as_view(), name='simple_login'),
  # Authentication -> Register
  path('accounts/register/basic/', views.basic_register, name='basic_register'),
  path('accounts/register/cover/', views.cover_register, name='cover_register'),
  path('accounts/register/illustration/', views.illustration_register, name='illustration_register'),
  path('accounts/register/simple/', views.simple_register, name='simple_register'),
  # Authentication -> Reset
  path('accounts/reset/basic/', views.BasicResetView.as_view(), name='basic_reset'),
  path('accounts/reset/cover/', views.CoverResetView.as_view(), name='cover_reset'),
  path('accounts/reset/illustration/', views.IllustrationResetView.as_view(), name='illustration_reset'),

  path('accounts/password-change/', views.UserPasswordChangeView.as_view(), name='password_change'),
  path('accounts/password-change-done/', auth_views.PasswordChangeDoneView.as_view(
      template_name='accounts/done/change-done.html'
  ), name="password_change_done"),
  path('accounts/password-reset-done/', auth_views.PasswordResetDoneView.as_view(
      template_name='accounts/done/reset-done.html'
  ), name='password_reset_done'),
  path('accounts/password-reset-confirm/<uidb64>/<token>/', 
      views.UserPasswordResetConfirmView.as_view(), name='password_reset_confirm'),
  path('accounts/password-reset-complete/', auth_views.PasswordResetCompleteView.as_view(
      template_name='accounts/complete/basic.html'
  ), name='password_reset_complete'),

  # Error
  path('accounts/error/', views.error, name="error"),
  path('accounts/error/', views.error, name="error"),
  path('accounts/error/404/', views.error_404, name="error_404"),
  path('accounts/error/500/', views.error_500, name="error_500"),

  path('coming-soon/', views.coming_soon, name='coming_soon'),
  path('2fa-security/', views.two_fa_security, name='two_fa_security'),
  path('logout/', views.logout_view, name="logout")
]
