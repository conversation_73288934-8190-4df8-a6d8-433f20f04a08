#!/usr/bin/env python3
"""
Test script to verify HTML entity decoding works correctly
"""

import html

def test_html_decode():
    """Test HTML entity decoding"""
    test_cases = [
        ("Games &amp; Toys", "Games & Toys"),
        ("Books &amp; Media", "Books & Media"),
        ("Home &amp; Garden", "Home & Garden"),
        ("Health &amp; Beauty", "Health & Beauty"),
        ("Sports &amp; Outdoors", "Sports & Outdoors"),
        ("Arts &amp; Crafts", "Arts & Crafts"),
        ("Food &amp; Beverage", "Food & Beverage"),
        ("&lt;Special&gt; Category", "<Special> Category"),
        ("&quot;Quoted&quot; Text", '"Quoted" Text'),
        ("&apos;Apostrophe&apos;", "'Apostrophe'"),
        ("Mixed &amp; &lt;entities&gt; &quot;here&quot;", 'Mixed & <entities> "here"'),
    ]
    
    print("Testing HTML entity decoding:")
    print("=" * 50)
    
    for encoded, expected in test_cases:
        decoded = html.unescape(encoded)
        status = "✓" if decoded == expected else "✗"
        print(f"{status} '{encoded}' -> '{decoded}'")
        if decoded != expected:
            print(f"   Expected: '{expected}'")
    
    print("\nPostgreSQL function equivalent:")
    print("=" * 50)
    
    # Show what the PostgreSQL function would do
    def postgres_decode(text):
        if text is None:
            return None
        return (text.replace('&amp;', '&')
                   .replace('&lt;', '<')
                   .replace('&gt;', '>')
                   .replace('&quot;', '"')
                   .replace('&apos;', "'"))
    
    for encoded, expected in test_cases:
        decoded = postgres_decode(encoded)
        status = "✓" if decoded == expected else "✗"
        print(f"{status} '{encoded}' -> '{decoded}'")

if __name__ == "__main__":
    test_html_decode()
